// ===== Car Details Page Specific JavaScript =====

document.addEventListener('DOMContentLoaded', function() {
    
    // Initialize car details functionality
    initImageGallery();
    initFavoriteButton();
    initShareButton();
    initContactButtons();
    initImageZoom();
    
});

function initImageGallery() {
    const mainImage = document.getElementById('mainImage');
    const thumbnails = document.querySelectorAll('.thumbnail');
    const prevBtn = document.getElementById('prevImage');
    const nextBtn = document.getElementById('nextImage');
    const currentImageIndex = document.getElementById('currentImageIndex');
    
    let currentIndex = 0;
    const totalImages = thumbnails.length;
    
    // Thumbnail click handlers
    thumbnails.forEach((thumbnail, index) => {
        thumbnail.addEventListener('click', function() {
            changeImage(index);
        });
    });
    
    // Navigation button handlers
    if (prevBtn) {
        prevBtn.addEventListener('click', function() {
            currentIndex = currentIndex > 0 ? currentIndex - 1 : totalImages - 1;
            changeImage(currentIndex);
        });
    }
    
    if (nextBtn) {
        nextBtn.addEventListener('click', function() {
            currentIndex = currentIndex < totalImages - 1 ? currentIndex + 1 : 0;
            changeImage(currentIndex);
        });
    }
    
    // Keyboard navigation
    document.addEventListener('keydown', function(e) {
        if (e.key === 'ArrowLeft') {
            prevBtn.click();
        } else if (e.key === 'ArrowRight') {
            nextBtn.click();
        }
    });
    
    // Auto-play slideshow (optional)
    let autoPlayInterval;
    
    function startAutoPlay() {
        autoPlayInterval = setInterval(() => {
            nextBtn.click();
        }, 5000);
    }
    
    function stopAutoPlay() {
        clearInterval(autoPlayInterval);
    }
    
    // Start auto-play
    startAutoPlay();
    
    // Pause auto-play on hover
    const gallery = document.querySelector('.car-gallery');
    if (gallery) {
        gallery.addEventListener('mouseenter', stopAutoPlay);
        gallery.addEventListener('mouseleave', startAutoPlay);
    }
    
    function changeImage(index) {
        currentIndex = index;
        
        // Update main image with fade effect
        mainImage.style.opacity = '0';
        setTimeout(() => {
            mainImage.src = thumbnails[index].src;
            mainImage.style.opacity = '1';
        }, 200);
        
        // Update active thumbnail
        thumbnails.forEach(thumb => thumb.classList.remove('active'));
        thumbnails[index].classList.add('active');
        
        // Update counter
        if (currentImageIndex) {
            currentImageIndex.textContent = index + 1;
        }
        
        // Add animation to thumbnail
        thumbnails[index].style.transform = 'scale(1.1)';
        setTimeout(() => {
            thumbnails[index].style.transform = 'scale(1)';
        }, 200);
    }
}

function initImageZoom() {
    const mainImage = document.getElementById('mainImage');
    
    if (mainImage) {
        // Create zoom lens
        const zoomLens = document.createElement('div');
        zoomLens.className = 'zoom-lens';
        zoomLens.style.display = 'none';
        
        // Create zoom result container
        const zoomResult = document.createElement('div');
        zoomResult.className = 'zoom-result';
        zoomResult.style.display = 'none';
        
        mainImage.parentNode.appendChild(zoomLens);
        mainImage.parentNode.appendChild(zoomResult);
        
        mainImage.addEventListener('mouseenter', function() {
            zoomLens.style.display = 'block';
            zoomResult.style.display = 'block';
        });
        
        mainImage.addEventListener('mouseleave', function() {
            zoomLens.style.display = 'none';
            zoomResult.style.display = 'none';
        });
        
        mainImage.addEventListener('mousemove', function(e) {
            const rect = this.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            // Position zoom lens
            zoomLens.style.left = (x - 50) + 'px';
            zoomLens.style.top = (y - 50) + 'px';
            
            // Calculate zoom position
            const zoomX = (x / rect.width) * 100;
            const zoomY = (y / rect.height) * 100;
            
            zoomResult.style.backgroundImage = `url(${this.src})`;
            zoomResult.style.backgroundPosition = `${zoomX}% ${zoomY}%`;
        });
    }
}

function initFavoriteButton() {
    const favoriteBtn = document.querySelector('.favorite-btn');
    
    if (favoriteBtn) {
        const carId = favoriteBtn.dataset.carId;
        
        // Load favorite state
        const favorites = JSON.parse(localStorage.getItem('favorites') || '[]');
        if (favorites.includes(carId)) {
            favoriteBtn.classList.add('active');
            favoriteBtn.innerHTML = '<i class="fas fa-heart me-1"></i>مضاف للمفضلة';
        }
        
        favoriteBtn.addEventListener('click', function(e) {
            e.preventDefault();
            
            const icon = this.querySelector('i');
            
            if (this.classList.contains('active')) {
                // Remove from favorites
                this.classList.remove('active');
                this.innerHTML = '<i class="far fa-heart me-1"></i>إضافة للمفضلة';
                removeFavorite(carId);
                showNotification('تم إزالة السيارة من المفضلة', 'info');
            } else {
                // Add to favorites
                this.classList.add('active');
                this.innerHTML = '<i class="fas fa-heart me-1"></i>مضاف للمفضلة';
                addFavorite(carId);
                showNotification('تم إضافة السيارة للمفضلة', 'success');
            }
            
            // Add animation
            this.style.transform = 'scale(1.1)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 200);
        });
    }
}

function initShareButton() {
    const shareBtn = document.querySelector('.share-btn');
    
    if (shareBtn) {
        shareBtn.addEventListener('click', function(e) {
            e.preventDefault();
            
            const carId = this.dataset.carId;
            const carUrl = window.location.href;
            const carTitle = document.querySelector('h1').textContent;
            
            // Check if Web Share API is supported
            if (navigator.share) {
                navigator.share({
                    title: carTitle,
                    text: 'شاهد هذه السيارة المميزة',
                    url: carUrl
                }).then(() => {
                    showNotification('تم مشاركة السيارة بنجاح', 'success');
                }).catch(() => {
                    showShareModal(carUrl, carTitle);
                });
            } else {
                showShareModal(carUrl, carTitle);
            }
        });
    }
}

function initContactButtons() {
    const contactButtons = document.querySelectorAll('.contact-buttons .btn');
    
    contactButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            // Add click animation
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 150);
            
            // Track contact method
            const method = this.textContent.trim();
            console.log(`Contact method used: ${method}`);
        });
    });
}

function addFavorite(carId) {
    let favorites = JSON.parse(localStorage.getItem('favorites') || '[]');
    if (!favorites.includes(carId)) {
        favorites.push(carId);
        localStorage.setItem('favorites', JSON.stringify(favorites));
    }
}

function removeFavorite(carId) {
    let favorites = JSON.parse(localStorage.getItem('favorites') || '[]');
    favorites = favorites.filter(id => id !== carId);
    localStorage.setItem('favorites', JSON.stringify(favorites));
}

function showShareModal(url, title) {
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">مشاركة ${title}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>شارك هذه السيارة مع الآخرين:</p>
                    
                    <div class="input-group mb-3">
                        <input type="text" class="form-control" value="${url}" readonly id="shareUrl">
                        <button class="btn btn-outline-primary" onclick="copyToClipboard('${url}')">
                            <i class="fas fa-copy me-1"></i>نسخ
                        </button>
                    </div>
                    
                    <div class="social-share">
                        <h6 class="mb-3">مشاركة عبر:</h6>
                        <div class="d-grid gap-2">
                            <a href="https://wa.me/?text=${encodeURIComponent(title + ' - ' + url)}" 
                               target="_blank" class="btn btn-success">
                                <i class="fab fa-whatsapp me-2"></i>واتساب
                            </a>
                            <a href="https://twitter.com/intent/tweet?text=${encodeURIComponent(title)}&url=${encodeURIComponent(url)}" 
                               target="_blank" class="btn btn-info">
                                <i class="fab fa-twitter me-2"></i>تويتر
                            </a>
                            <a href="https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}" 
                               target="_blank" class="btn btn-primary">
                                <i class="fab fa-facebook me-2"></i>فيسبوك
                            </a>
                            <a href="mailto:?subject=${encodeURIComponent(title)}&body=${encodeURIComponent(url)}" 
                               class="btn btn-secondary">
                                <i class="fas fa-envelope me-2"></i>البريد الإلكتروني
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();
    
    modal.addEventListener('hidden.bs.modal', () => {
        modal.remove();
    });
}

function copyToClipboard(text) {
    if (navigator.clipboard) {
        navigator.clipboard.writeText(text).then(() => {
            showNotification('تم نسخ الرابط بنجاح', 'success');
        });
    } else {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showNotification('تم نسخ الرابط بنجاح', 'success');
    }
}

// Add custom styles for image zoom and gallery
const style = document.createElement('style');
style.textContent = `
    .main-image-container {
        position: relative;
        overflow: hidden;
        border-radius: 10px;
        cursor: zoom-in;
    }
    
    .main-image {
        transition: opacity 0.3s ease;
        width: 100%;
        height: 400px;
        object-fit: cover;
    }
    
    .image-nav {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        width: 100%;
        display: flex;
        justify-content: space-between;
        padding: 0 15px;
        opacity: 0;
        transition: opacity 0.3s ease;
    }
    
    .main-image-container:hover .image-nav {
        opacity: 1;
    }
    
    .image-nav .btn {
        background: rgba(0,0,0,0.7);
        border: none;
        color: white;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .image-counter {
        position: absolute;
        bottom: 15px;
        right: 15px;
        background: rgba(0,0,0,0.7);
        color: white;
        padding: 5px 10px;
        border-radius: 15px;
        font-size: 0.9rem;
    }
    
    .zoom-lens {
        position: absolute;
        width: 100px;
        height: 100px;
        border: 2px solid #007bff;
        border-radius: 50%;
        pointer-events: none;
        background: rgba(0,123,255,0.1);
    }
    
    .zoom-result {
        position: absolute;
        top: 0;
        right: -320px;
        width: 300px;
        height: 300px;
        border: 1px solid #ddd;
        border-radius: 10px;
        background-repeat: no-repeat;
        background-size: 800px 600px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        z-index: 10;
    }
    
    .specs-card {
        background: white;
        padding: 25px;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        position: sticky;
        top: 100px;
    }
    
    .contact-card {
        background: white;
        padding: 25px;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    
    .price-box {
        background: rgba(255,255,255,0.1);
        padding: 20px;
        border-radius: 10px;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255,255,255,0.2);
    }
    
    .price-amount {
        font-size: 2rem;
        color: white;
    }
    
    .feature-item {
        padding: 8px 0;
        border-bottom: 1px solid #f0f0f0;
        transition: all 0.3s ease;
    }
    
    .feature-item:hover {
        background: #f8f9fa;
        padding-left: 10px;
    }
    
    .feature-item:last-child {
        border-bottom: none;
    }
    
    .breadcrumb {
        background: transparent;
        padding: 0;
    }
    
    .breadcrumb-item + .breadcrumb-item::before {
        color: rgba(255,255,255,0.7);
    }
    
    @media (max-width: 768px) {
        .zoom-result {
            display: none;
        }
        
        .specs-card {
            position: static;
        }
        
        .price-amount {
            font-size: 1.5rem;
        }
    }
`;
document.head.appendChild(style);
