# معرض السيارات الفاخرة | Luxury Car Showroom

موقع معرض سيارات متقدم وعصري مطور باستخدام PHP 8 و Bootstrap 5.3 مع دعم اللغتين العربية والإنجليزية.

## المميزات الرئيسية

### 🚗 إدارة السيارات
- عرض السيارات مع فلترة متقدمة
- البحث الذكي في السيارات
- صفحة تفاصيل مفصلة لكل سيارة
- معرض صور تفاعلي مع تكبير
- نظام المفضلة
- مشاركة السيارات عبر وسائل التواصل

### 🌐 دعم متعدد اللغات
- اللغة العربية (RTL)
- اللغة الإنجليزية (LTR)
- تبديل سهل بين اللغات
- ترجمة شاملة لجميع النصوص

### 🎨 تصميم متقدم
- تصميم عصري ومتجاوب
- تأثيرات بصرية متقدمة
- انيميشن AOS
- تأثيرات hover تفاعلية
- تصميم متوافق مع جميع الأجهزة

### 📱 واجهة مستخدم متميزة
- تصميم متجاوب بالكامل
- تحميل سريع مع شاشة تحميل
- رسائل تفاعلية
- نماذج محققة
- تجربة مستخدم سلسة

### 🔧 مميزات تقنية
- PHP 8 مع PDO
- Bootstrap 5.3
- MySQL/MariaDB
- JavaScript ES6+
- CSS3 متقدم
- Font Awesome Icons
- Google Fonts

## متطلبات النظام

- PHP 8.0 أو أحدث
- MySQL 5.7 أو MariaDB 10.3 أو أحدث
- Apache/Nginx
- mod_rewrite مفعل

## التثبيت

### 1. تحميل الملفات
```bash
git clone [repository-url]
cd car-showroom
```

### 2. إعداد قاعدة البيانات
```sql
-- إنشاء قاعدة البيانات
CREATE DATABASE car_showroom CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- استيراد البيانات
mysql -u username -p car_showroom < database.sql
```

### 3. تكوين الاتصال
قم بتعديل ملف `config.php`:
```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'car_showroom');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
```

### 4. إعداد الخادم
- تأكد من أن مجلد المشروع في مجلد الويب
- تأكد من صلاحيات الكتابة لمجلد `assets/images`
- فعل mod_rewrite إذا كنت تستخدم Apache

## هيكل المشروع

```
car-showroom/
├── assets/
│   ├── css/
│   │   └── style.css          # ملف CSS الرئيسي
│   ├── js/
│   │   ├── main.js           # JavaScript الرئيسي
│   │   ├── home.js           # JavaScript الصفحة الرئيسية
│   │   ├── cars.js           # JavaScript صفحة السيارات
│   │   ├── car-details.js    # JavaScript تفاصيل السيارة
│   │   └── contact.js        # JavaScript صفحة الاتصال
│   └── images/
│       ├── cars/             # صور السيارات
│       └── gallery/          # صور المعرض
├── includes/
│   ├── header.php            # الهيدر المشترك
│   └── footer.php            # الفوتر المشترك
├── lang/
│   ├── ar/
│   │   └── translations.php  # ترجمات عربية
│   └── en/
│       └── translations.php  # ترجمات إنجليزية
├── pages/                    # صفحات إضافية
├── admin/                    # لوحة الإدارة (مستقبلية)
├── config.php               # إعدادات النظام
├── index.php               # الصفحة الرئيسية
├── cars.php                # صفحة السيارات
├── car-details.php         # تفاصيل السيارة
├── contact.php             # صفحة الاتصال
├── about.php               # صفحة من نحن
├── database.sql            # قاعدة البيانات
└── README.md              # هذا الملف
```

## الصفحات المتاحة

### 🏠 الصفحة الرئيسية (`index.php`)
- قسم البطل مع تأثيرات متحركة
- عرض السيارات المميزة
- قسم "لماذا تختارنا"
- عرض الماركات الشائعة

### 🚗 صفحة السيارات (`cars.php`)
- عرض جميع السيارات
- فلترة متقدمة (الماركة، السعر، السنة، الحالة)
- بحث ذكي
- ترتيب متعدد الخيارات
- نظام المفضلة والمشاركة

### 📋 تفاصيل السيارة (`car-details.php`)
- معرض صور تفاعلي مع تكبير
- مواصفات مفصلة
- قائمة المميزات
- معلومات الاتصال
- سيارات مشابهة

### 📞 صفحة الاتصال (`contact.php`)
- نموذج اتصال محقق
- معلومات الاتصال
- خريطة تفاعلية
- أسئلة شائعة
- روابط التواصل السريع

### ℹ️ صفحة من نحن (`about.php`)
- قصة الشركة
- الرؤية والرسالة والقيم
- فريق العمل
- الإنجازات
- آراء العملاء

## المميزات التقنية

### 🔒 الأمان
- حماية CSRF
- تنظيف البيانات المدخلة
- استعلامات محضرة (Prepared Statements)
- التحقق من صحة البيانات

### 🚀 الأداء
- تحميل الصور الكسول (Lazy Loading)
- ضغط CSS و JavaScript
- استخدام CDN للمكتبات
- فهرسة قاعدة البيانات المحسنة

### 📱 التجاوب
- تصميم متجاوب بالكامل
- دعم جميع أحجام الشاشات
- تحسين للأجهزة اللوحية والهواتف
- تجربة مستخدم محسنة

## التخصيص

### إضافة لغة جديدة
1. أنشئ مجلد جديد في `lang/` (مثل `lang/fr/`)
2. أنشئ ملف `translations.php` مع الترجمات
3. أضف اللغة إلى `SUPPORTED_LANGS` في `config.php`

### تخصيص التصميم
- عدل ملف `assets/css/style.css`
- استخدم متغيرات CSS المعرفة في `:root`
- أضف تأثيرات جديدة في ملفات JavaScript

### إضافة صفحات جديدة
1. أنشئ ملف PHP جديد
2. ضمن `config.php`
3. استخدم `includes/header.php` و `includes/footer.php`
4. أضف الترجمات المطلوبة

## الدعم والمساهمة

### الإبلاغ عن مشاكل
- استخدم نظام Issues في GitHub
- قدم وصف مفصل للمشكلة
- أرفق لقطات شاشة إن أمكن

### المساهمة
1. Fork المشروع
2. أنشئ branch جديد للميزة
3. اكتب كود نظيف ومعلق
4. اختبر التغييرات
5. أرسل Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف LICENSE للتفاصيل.

## الاتصال

- البريد الإلكتروني: <EMAIL>
- الهاتف: +966 12 345 6789
- الموقع: [www.carshowroom.com](http://www.carshowroom.com)

---

تم تطوير هذا المشروع بعناية لتقديم تجربة مستخدم متميزة في عالم معارض السيارات الرقمية.
