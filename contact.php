<?php
require_once 'config.php';

$page_title = t('contact');
$page_scripts = ['assets/js/contact.js'];

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = sanitize($_POST['name'] ?? '');
    $email = sanitize($_POST['email'] ?? '');
    $phone = sanitize($_POST['phone'] ?? '');
    $subject = sanitize($_POST['subject'] ?? '');
    $message = sanitize($_POST['message'] ?? '');
    $car_id = sanitize($_POST['car_id'] ?? '');
    $csrf_token = $_POST['csrf_token'] ?? '';
    
    $errors = [];
    
    // Validate CSRF token
    if (!verifyCSRFToken($csrf_token)) {
        $errors[] = t('invalid_request', 'طلب غير صالح');
    }
    
    // Validate required fields
    if (empty($name)) {
        $errors[] = t('name_required', 'الاسم مطلوب');
    }
    
    if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = t('valid_email_required', 'بريد إلكتروني صالح مطلوب');
    }
    
    if (empty($phone)) {
        $errors[] = t('phone_required', 'رقم الهاتف مطلوب');
    }
    
    if (empty($subject)) {
        $errors[] = t('subject_required', 'الموضوع مطلوب');
    }
    
    if (empty($message)) {
        $errors[] = t('message_required', 'الرسالة مطلوبة');
    }
    
    if (empty($errors)) {
        // In a real application, you would save to database and send email
        // For now, we'll just show a success message
        
        // Simulate email sending
        $email_sent = true; // This would be the result of mail() function
        
        if ($email_sent) {
            showMessage(t('message_sent'), 'success');
            // Clear form data
            $name = $email = $phone = $subject = $message = $car_id = '';
        } else {
            showMessage(t('message_error'), 'error');
        }
    } else {
        foreach ($errors as $error) {
            showMessage($error, 'error');
        }
    }
}

// Get car ID from URL if specified
$car_id = isset($_GET['car']) ? (int)$_GET['car'] : 0;
$car_name = '';

if ($car_id) {
    // In real application, fetch car name from database
    $cars = [
        1 => 'BMW X5 2024',
        2 => 'Mercedes C-Class 2023',
        3 => 'Audi A6 2023'
    ];
    $car_name = $cars[$car_id] ?? '';
}

include 'includes/header.php';
?>

<!-- Page Header -->
<section class="page-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="display-4 fw-bold mb-3" data-aos="fade-right">
                    <i class="fas fa-envelope me-3"></i><?php echo t('contact_us'); ?>
                </h1>
                <p class="lead mb-0" data-aos="fade-right" data-aos-delay="200">
                    <?php echo t('contact_us_desc', 'نحن هنا لمساعدتك في العثور على السيارة المثالية'); ?>
                </p>
            </div>
            <div class="col-lg-4 text-lg-end" data-aos="fade-left" data-aos-delay="300">
                <div class="contact-hours">
                    <h5 class="fw-bold mb-2"><?php echo t('working_hours', 'ساعات العمل'); ?></h5>
                    <p class="mb-1"><?php echo t('sat_thu', 'السبت - الخميس'); ?>: 9:00 ص - 10:00 م</p>
                    <p class="mb-0"><?php echo t('friday', 'الجمعة'); ?>: 2:00 م - 10:00 م</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Contact Section -->
<section class="contact-section py-5">
    <div class="container">
        <div class="row">
            <!-- Contact Form -->
            <div class="col-lg-8 mb-4">
                <div class="contact-form" data-aos="fade-up">
                    <h3 class="mb-4"><?php echo t('send_message', 'إرسال رسالة'); ?></h3>
                    
                    <?php if ($car_name): ?>
                        <div class="alert alert-info" role="alert">
                            <i class="fas fa-car me-2"></i>
                            <?php echo t('inquiry_about', 'استفسار حول'); ?>: <strong><?php echo htmlspecialchars($car_name); ?></strong>
                        </div>
                    <?php endif; ?>
                    
                    <form method="POST" class="needs-validation" novalidate id="contactForm">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        <input type="hidden" name="car_id" value="<?php echo $car_id; ?>">
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">
                                    <?php echo t('full_name'); ?> <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       value="<?php echo htmlspecialchars($name ?? ''); ?>" required>
                                <div class="invalid-feedback">
                                    <?php echo t('name_required', 'الاسم مطلوب'); ?>
                                </div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">
                                    <?php echo t('email'); ?> <span class="text-danger">*</span>
                                </label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="<?php echo htmlspecialchars($email ?? ''); ?>" required>
                                <div class="invalid-feedback">
                                    <?php echo t('valid_email_required', 'بريد إلكتروني صالح مطلوب'); ?>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">
                                    <?php echo t('phone'); ?> <span class="text-danger">*</span>
                                </label>
                                <input type="tel" class="form-control" id="phone" name="phone" 
                                       value="<?php echo htmlspecialchars($phone ?? ''); ?>" required>
                                <div class="invalid-feedback">
                                    <?php echo t('phone_required', 'رقم الهاتف مطلوب'); ?>
                                </div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="subject" class="form-label">
                                    <?php echo t('subject'); ?> <span class="text-danger">*</span>
                                </label>
                                <select class="form-select" id="subject" name="subject" required>
                                    <option value=""><?php echo t('select_subject', 'اختر الموضوع'); ?></option>
                                    <option value="car_inquiry" <?php echo ($subject ?? '') === 'car_inquiry' ? 'selected' : ''; ?>>
                                        <?php echo t('car_inquiry', 'استفسار عن سيارة'); ?>
                                    </option>
                                    <option value="financing" <?php echo ($subject ?? '') === 'financing' ? 'selected' : ''; ?>>
                                        <?php echo t('financing'); ?>
                                    </option>
                                    <option value="trade_in" <?php echo ($subject ?? '') === 'trade_in' ? 'selected' : ''; ?>>
                                        <?php echo t('trade_in'); ?>
                                    </option>
                                    <option value="maintenance" <?php echo ($subject ?? '') === 'maintenance' ? 'selected' : ''; ?>>
                                        <?php echo t('maintenance'); ?>
                                    </option>
                                    <option value="general" <?php echo ($subject ?? '') === 'general' ? 'selected' : ''; ?>>
                                        <?php echo t('general_inquiry', 'استفسار عام'); ?>
                                    </option>
                                </select>
                                <div class="invalid-feedback">
                                    <?php echo t('subject_required', 'الموضوع مطلوب'); ?>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="message" class="form-label">
                                <?php echo t('message'); ?> <span class="text-danger">*</span>
                            </label>
                            <textarea class="form-control" id="message" name="message" rows="5" 
                                      placeholder="<?php echo t('message_placeholder', 'اكتب رسالتك هنا...'); ?>" required><?php echo htmlspecialchars($message ?? ''); ?></textarea>
                            <div class="invalid-feedback">
                                <?php echo t('message_required', 'الرسالة مطلوبة'); ?>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="privacy" required>
                                <label class="form-check-label" for="privacy">
                                    <?php echo t('privacy_agreement', 'أوافق على سياسة الخصوصية وشروط الاستخدام'); ?> <span class="text-danger">*</span>
                                </label>
                                <div class="invalid-feedback">
                                    <?php echo t('privacy_required', 'يجب الموافقة على سياسة الخصوصية'); ?>
                                </div>
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary btn-lg" id="submitBtn">
                            <i class="fas fa-paper-plane me-2"></i><?php echo t('send_message'); ?>
                            <span class="spinner-border spinner-border-sm ms-2 d-none" role="status"></span>
                        </button>
                    </form>
                </div>
            </div>
            
            <!-- Contact Information -->
            <div class="col-lg-4">
                <div class="contact-info" data-aos="fade-up" data-aos-delay="200">
                    <h3 class="mb-4 text-white"><?php echo t('contact_information', 'معلومات الاتصال'); ?></h3>
                    
                    <div class="contact-item">
                        <i class="fas fa-map-marker-alt"></i>
                        <div>
                            <h5><?php echo t('address', 'العنوان'); ?></h5>
                            <p><?php echo $current_lang === 'ar' ? 'شارع الملك فهد، الرياض 12345، المملكة العربية السعودية' : 'King Fahd Road, Riyadh 12345, Saudi Arabia'; ?></p>
                        </div>
                    </div>
                    
                    <div class="contact-item">
                        <i class="fas fa-phone"></i>
                        <div>
                            <h5><?php echo t('phone'); ?></h5>
                            <p><a href="tel:+966123456789" class="text-white">+966 12 345 6789</a></p>
                        </div>
                    </div>
                    
                    <div class="contact-item">
                        <i class="fas fa-envelope"></i>
                        <div>
                            <h5><?php echo t('email'); ?></h5>
                            <p><a href="mailto:<EMAIL>" class="text-white"><EMAIL></a></p>
                        </div>
                    </div>
                    
                    <div class="contact-item">
                        <i class="fas fa-clock"></i>
                        <div>
                            <h5><?php echo t('working_hours', 'ساعات العمل'); ?></h5>
                            <p><?php echo t('sat_thu', 'السبت - الخميس'); ?>: 9:00 ص - 10:00 م<br>
                               <?php echo t('friday', 'الجمعة'); ?>: 2:00 م - 10:00 م</p>
                        </div>
                    </div>
                    
                    <div class="social-links mt-4">
                        <h5 class="text-white mb-3"><?php echo t('follow_us', 'تابعنا'); ?></h5>
                        <a href="#" class="social-link"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="social-link"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="social-link"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="social-link"><i class="fab fa-linkedin-in"></i></a>
                        <a href="#" class="social-link"><i class="fab fa-youtube"></i></a>
                    </div>
                </div>
                
                <!-- Quick Contact -->
                <div class="quick-contact mt-4" data-aos="fade-up" data-aos-delay="400">
                    <h4 class="mb-3"><?php echo t('quick_contact', 'اتصال سريع'); ?></h4>
                    <div class="d-grid gap-2">
                        <a href="tel:+966123456789" class="btn btn-success">
                            <i class="fas fa-phone me-2"></i><?php echo t('call_now', 'اتصل الآن'); ?>
                        </a>
                        <a href="https://wa.me/966123456789" target="_blank" class="btn btn-success">
                            <i class="fab fa-whatsapp me-2"></i><?php echo t('whatsapp'); ?>
                        </a>
                        <a href="mailto:<EMAIL>" class="btn btn-outline-primary">
                            <i class="fas fa-envelope me-2"></i><?php echo t('send_email', 'إرسال بريد'); ?>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Map Section -->
<section class="map-section py-5 bg-light">
    <div class="container">
        <div class="section-title" data-aos="fade-up">
            <h2><?php echo t('find_us', 'اعثر علينا'); ?></h2>
            <p><?php echo t('visit_showroom', 'قم بزيارة معرضنا لمشاهدة السيارات عن قرب'); ?></p>
        </div>
        
        <div class="map-container" data-aos="fade-up" data-aos-delay="200">
            <!-- Google Maps Embed -->
            <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3624.1234567890!2d46.6753!3d24.7136!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zMjTCsDQyJzQ5LjAiTiA0NsKwNDAnMzEuMSJF!5e0!3m2!1sen!2ssa!4v1234567890" 
                    width="100%" height="400" style="border:0; border-radius: 10px;" allowfullscreen="" loading="lazy"></iframe>
        </div>
    </div>
</section>

<!-- FAQ Section -->
<section class="faq-section py-5">
    <div class="container">
        <div class="section-title" data-aos="fade-up">
            <h2><?php echo t('frequently_asked_questions', 'الأسئلة الشائعة'); ?></h2>
            <p><?php echo t('faq_desc', 'إجابات على الأسئلة الأكثر شيوعاً'); ?></p>
        </div>
        
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="accordion" id="faqAccordion" data-aos="fade-up" data-aos-delay="200">
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
                                <?php echo t('faq_financing', 'هل تقدمون خدمات التمويل؟'); ?>
                            </button>
                        </h2>
                        <div id="faq1" class="accordion-collapse collapse show" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                <?php echo t('faq_financing_answer', 'نعم، نقدم خدمات التمويل بالتعاون مع أفضل البنوك والمؤسسات المالية.'); ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
                                <?php echo t('faq_warranty', 'ما هي فترة الضمان؟'); ?>
                            </button>
                        </h2>
                        <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                <?php echo t('faq_warranty_answer', 'نقدم ضمان شامل لمدة سنتين أو 100,000 كيلومتر أيهما أقل.'); ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq3">
                                <?php echo t('faq_trade_in', 'هل تقبلون استبدال السيارات؟'); ?>
                            </button>
                        </h2>
                        <div id="faq3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                <?php echo t('faq_trade_in_answer', 'نعم، نقبل استبدال السيارات المستعملة بأفضل الأسعار في السوق.'); ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<?php include 'includes/footer.php'; ?>
