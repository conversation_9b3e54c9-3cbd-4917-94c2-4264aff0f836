# Car Showroom .htaccess Configuration
# تكوين Apache لموقع معرض السيارات

# ===== Security Headers =====
<IfModule mod_headers.c>
    # Prevent MIME type sniffing
    Header always set X-Content-Type-Options nosniff
    
    # Enable XSS protection
    Header always set X-XSS-Protection "1; mode=block"
    
    # Prevent clickjacking
    Header always set X-Frame-Options DENY
    
    # Referrer Policy
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    
    # Content Security Policy
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://unpkg.com https://www.google.com https://maps.googleapis.com; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://fonts.googleapis.com https://unpkg.com; font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com; img-src 'self' data: https: http:; connect-src 'self'; frame-src https://www.google.com https://maps.google.com;"
    
    # Remove server signature
    Header always unset Server
    Header always unset X-Powered-By
</IfModule>

# ===== URL Rewriting =====
<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # Force HTTPS (uncomment if you have SSL)
    # RewriteCond %{HTTPS} off
    # RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
    
    # Remove www (optional)
    # RewriteCond %{HTTP_HOST} ^www\.(.*)$ [NC]
    # RewriteRule ^(.*)$ https://%1/$1 [R=301,L]
    
    # Pretty URLs for car details
    RewriteRule ^car/([0-9]+)/?$ car-details.php?id=$1 [L,QSA]
    RewriteRule ^car/([0-9]+)/([a-zA-Z0-9-]+)/?$ car-details.php?id=$1 [L,QSA]
    
    # Pretty URLs for pages
    RewriteRule ^cars/?$ cars.php [L,QSA]
    RewriteRule ^contact/?$ contact.php [L,QSA]
    RewriteRule ^about/?$ about.php [L,QSA]
    
    # Language switching
    RewriteRule ^ar/?$ index.php?lang=ar [L,QSA]
    RewriteRule ^en/?$ index.php?lang=en [L,QSA]
    
    # Block access to sensitive files
    RewriteRule ^(config\.php|database\.sql|\.env)$ - [F,L]
    
    # Block access to directories
    RewriteRule ^(includes|lang)/ - [F,L]
</IfModule>

# ===== File Compression =====
<IfModule mod_deflate.c>
    # Compress HTML, CSS, JavaScript, Text, XML and fonts
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/vnd.ms-fontobject
    AddOutputFilterByType DEFLATE application/x-font
    AddOutputFilterByType DEFLATE application/x-font-opentype
    AddOutputFilterByType DEFLATE application/x-font-otf
    AddOutputFilterByType DEFLATE application/x-font-truetype
    AddOutputFilterByType DEFLATE application/x-font-ttf
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE font/opentype
    AddOutputFilterByType DEFLATE font/otf
    AddOutputFilterByType DEFLATE font/ttf
    AddOutputFilterByType DEFLATE image/svg+xml
    AddOutputFilterByType DEFLATE image/x-icon
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/javascript
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/xml
    
    # Remove browser bugs (only needed for really old browsers)
    BrowserMatch ^Mozilla/4 gzip-only-text/html
    BrowserMatch ^Mozilla/4\.0[678] no-gzip
    BrowserMatch \bMSIE !no-gzip !gzip-only-text/html
    Header append Vary User-Agent
</IfModule>

# ===== Browser Caching =====
<IfModule mod_expires.c>
    ExpiresActive on
    
    # Images
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/webp "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
    
    # CSS and JavaScript
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"
    
    # Fonts
    ExpiresByType font/ttf "access plus 1 year"
    ExpiresByType font/otf "access plus 1 year"
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType application/font-woff "access plus 1 year"
    
    # HTML and XML
    ExpiresByType text/html "access plus 1 hour"
    ExpiresByType text/xml "access plus 1 hour"
    ExpiresByType application/xml "access plus 1 hour"
    ExpiresByType application/json "access plus 1 hour"
    
    # Default
    ExpiresDefault "access plus 1 week"
</IfModule>

# ===== Cache Control Headers =====
<IfModule mod_headers.c>
    # Cache static assets
    <FilesMatch "\.(css|js|png|jpg|jpeg|gif|webp|svg|ico|woff|woff2|ttf|otf)$">
        Header set Cache-Control "public, max-age=2592000"
    </FilesMatch>
    
    # Don't cache HTML files
    <FilesMatch "\.(html|htm|php)$">
        Header set Cache-Control "no-cache, no-store, must-revalidate"
        Header set Pragma "no-cache"
        Header set Expires 0
    </FilesMatch>
</IfModule>

# ===== File Protection =====
# Protect config files
<Files "config.php">
    Order Allow,Deny
    Deny from all
</Files>

<Files "database.sql">
    Order Allow,Deny
    Deny from all
</Files>

<Files ".env">
    Order Allow,Deny
    Deny from all
</Files>

# Protect .htaccess
<Files ".htaccess">
    Order Allow,Deny
    Deny from all
</Files>

# Block access to backup files
<FilesMatch "\.(bak|backup|old|tmp|temp)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# ===== Error Pages =====
ErrorDocument 404 /404.php
ErrorDocument 403 /403.php
ErrorDocument 500 /500.php

# ===== PHP Settings =====
<IfModule mod_php8.c>
    # Hide PHP version
    php_flag expose_php off
    
    # Security settings
    php_flag register_globals off
    php_flag magic_quotes_gpc off
    php_flag allow_url_fopen off
    php_flag allow_url_include off
    
    # Performance settings
    php_value memory_limit 256M
    php_value max_execution_time 30
    php_value max_input_time 30
    php_value post_max_size 20M
    php_value upload_max_filesize 10M
    
    # Error handling
    php_flag display_errors off
    php_flag log_errors on
    php_value error_log /var/log/php_errors.log
</IfModule>

# ===== MIME Types =====
<IfModule mod_mime.c>
    # Web fonts
    AddType application/font-woff woff
    AddType application/font-woff2 woff2
    AddType application/vnd.ms-fontobject eot
    AddType application/x-font-ttf ttf
    AddType font/opentype otf
    
    # Images
    AddType image/webp webp
    AddType image/svg+xml svg
    
    # Other
    AddType application/json json
    AddType application/ld+json jsonld
</IfModule>

# ===== Directory Browsing =====
Options -Indexes

# ===== Follow Symbolic Links =====
Options +FollowSymLinks

# ===== Server Signature =====
ServerSignature Off

# ===== Hotlink Protection =====
<IfModule mod_rewrite.c>
    RewriteCond %{HTTP_REFERER} !^$
    RewriteCond %{HTTP_REFERER} !^https?://(www\.)?yourdomain\.com [NC]
    RewriteCond %{REQUEST_URI} \.(jpe?g|png|gif|webp)$ [NC]
    RewriteRule \.(jpe?g|png|gif|webp)$ - [F]
</IfModule>

# ===== Rate Limiting (if mod_evasive is available) =====
<IfModule mod_evasive24.c>
    DOSHashTableSize    2048
    DOSPageCount        10
    DOSSiteCount        50
    DOSPageInterval     1
    DOSSiteInterval     1
    DOSBlockingPeriod   600
</IfModule>
