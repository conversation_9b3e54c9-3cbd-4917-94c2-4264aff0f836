<?php
require_once 'config.php';

$page_title = t('cars');
$page_scripts = ['assets/js/cars.js'];

// Sample cars data (in real application, this would come from database)
$cars = [
    [
        'id' => 1,
        'name' => 'BMW X5 2024',
        'brand' => 'BMW',
        'year' => 2024,
        'price' => 450000,
        'mileage' => 0,
        'fuel_type' => 'petrol',
        'transmission' => 'automatic',
        'condition' => 'new',
        'image' => 'https://via.placeholder.com/400x250/007bff/ffffff?text=BMW+X5',
        'featured' => true
    ],
    [
        'id' => 2,
        'name' => 'Mercedes C-Class 2023',
        'brand' => 'Mercedes',
        'year' => 2023,
        'price' => 320000,
        'mileage' => 15000,
        'fuel_type' => 'petrol',
        'transmission' => 'automatic',
        'condition' => 'excellent',
        'image' => 'https://via.placeholder.com/400x250/28a745/ffffff?text=Mercedes+C-Class',
        'featured' => true
    ],
    [
        'id' => 3,
        'name' => 'Audi A6 2023',
        'brand' => 'Audi',
        'year' => 2023,
        'price' => 280000,
        'mileage' => 8500,
        'fuel_type' => 'petrol',
        'transmission' => 'automatic',
        'condition' => 'excellent',
        'image' => 'https://via.placeholder.com/400x250/dc3545/ffffff?text=Audi+A6',
        'featured' => false
    ],
    [
        'id' => 4,
        'name' => 'Toyota Camry 2023',
        'brand' => 'Toyota',
        'year' => 2023,
        'price' => 150000,
        'mileage' => 25000,
        'fuel_type' => 'petrol',
        'transmission' => 'automatic',
        'condition' => 'good',
        'image' => 'https://via.placeholder.com/400x250/ffc107/ffffff?text=Toyota+Camry',
        'featured' => false
    ],
    [
        'id' => 5,
        'name' => 'Lexus ES 2022',
        'brand' => 'Lexus',
        'year' => 2022,
        'price' => 220000,
        'mileage' => 35000,
        'fuel_type' => 'hybrid',
        'transmission' => 'automatic',
        'condition' => 'good',
        'image' => 'https://via.placeholder.com/400x250/17a2b8/ffffff?text=Lexus+ES',
        'featured' => false
    ],
    [
        'id' => 6,
        'name' => 'Porsche Macan 2024',
        'brand' => 'Porsche',
        'year' => 2024,
        'price' => 380000,
        'mileage' => 0,
        'fuel_type' => 'petrol',
        'transmission' => 'automatic',
        'condition' => 'new',
        'image' => 'https://via.placeholder.com/400x250/6f42c1/ffffff?text=Porsche+Macan',
        'featured' => true
    ]
];

// Get filter parameters
$search = isset($_GET['search']) ? sanitize($_GET['search']) : '';
$brand_filter = isset($_GET['brand']) ? sanitize($_GET['brand']) : '';
$price_min = isset($_GET['price_min']) ? (int)$_GET['price_min'] : 0;
$price_max = isset($_GET['price_max']) ? (int)$_GET['price_max'] : 1000000;
$year_min = isset($_GET['year_min']) ? (int)$_GET['year_min'] : 2020;
$condition_filter = isset($_GET['condition']) ? sanitize($_GET['condition']) : '';
$sort_by = isset($_GET['sort']) ? sanitize($_GET['sort']) : 'price_asc';

// Filter cars
$filtered_cars = array_filter($cars, function($car) use ($search, $brand_filter, $price_min, $price_max, $year_min, $condition_filter) {
    $match_search = empty($search) || stripos($car['name'], $search) !== false;
    $match_brand = empty($brand_filter) || $car['brand'] === $brand_filter;
    $match_price = $car['price'] >= $price_min && $car['price'] <= $price_max;
    $match_year = $car['year'] >= $year_min;
    $match_condition = empty($condition_filter) || $car['condition'] === $condition_filter;
    
    return $match_search && $match_brand && $match_price && $match_year && $match_condition;
});

// Sort cars
usort($filtered_cars, function($a, $b) use ($sort_by) {
    switch ($sort_by) {
        case 'price_asc':
            return $a['price'] - $b['price'];
        case 'price_desc':
            return $b['price'] - $a['price'];
        case 'year_desc':
            return $b['year'] - $a['year'];
        case 'year_asc':
            return $a['year'] - $b['year'];
        case 'name_asc':
            return strcmp($a['name'], $b['name']);
        default:
            return 0;
    }
});

include 'includes/header.php';
?>

<!-- Page Header -->
<section class="page-header bg-gradient text-white py-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="display-4 fw-bold mb-3" data-aos="fade-right">
                    <i class="fas fa-car me-3"></i><?php echo t('all_cars'); ?>
                </h1>
                <p class="lead mb-0" data-aos="fade-right" data-aos-delay="200">
                    <?php echo t('discover_premium_cars', 'اكتشف مجموعتنا المميزة من السيارات الفاخرة'); ?>
                </p>
            </div>
            <div class="col-lg-4 text-lg-end" data-aos="fade-left" data-aos-delay="300">
                <div class="stats-box">
                    <h3 class="fw-bold"><?php echo count($filtered_cars); ?></h3>
                    <p class="mb-0"><?php echo t('cars_available', 'سيارة متاحة'); ?></p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Filters Section -->
<section class="filters-section py-4 bg-light">
    <div class="container">
        <form method="GET" class="filters-form" id="filtersForm">
            <div class="row g-3">
                <!-- Search -->
                <div class="col-lg-3 col-md-6">
                    <div class="form-group">
                        <label class="form-label fw-semibold">
                            <i class="fas fa-search me-1"></i><?php echo t('search'); ?>
                        </label>
                        <input type="text" class="form-control" name="search" value="<?php echo htmlspecialchars($search); ?>" 
                               placeholder="<?php echo t('search_cars'); ?>">
                    </div>
                </div>
                
                <!-- Brand Filter -->
                <div class="col-lg-2 col-md-6">
                    <div class="form-group">
                        <label class="form-label fw-semibold">
                            <i class="fas fa-car me-1"></i><?php echo t('brand', 'الماركة'); ?>
                        </label>
                        <select class="form-select" name="brand">
                            <option value=""><?php echo t('all_brands', 'جميع الماركات'); ?></option>
                            <option value="BMW" <?php echo $brand_filter === 'BMW' ? 'selected' : ''; ?>>BMW</option>
                            <option value="Mercedes" <?php echo $brand_filter === 'Mercedes' ? 'selected' : ''; ?>>Mercedes</option>
                            <option value="Audi" <?php echo $brand_filter === 'Audi' ? 'selected' : ''; ?>>Audi</option>
                            <option value="Toyota" <?php echo $brand_filter === 'Toyota' ? 'selected' : ''; ?>>Toyota</option>
                            <option value="Lexus" <?php echo $brand_filter === 'Lexus' ? 'selected' : ''; ?>>Lexus</option>
                            <option value="Porsche" <?php echo $brand_filter === 'Porsche' ? 'selected' : ''; ?>>Porsche</option>
                        </select>
                    </div>
                </div>
                
                <!-- Price Range -->
                <div class="col-lg-2 col-md-6">
                    <div class="form-group">
                        <label class="form-label fw-semibold">
                            <i class="fas fa-dollar-sign me-1"></i><?php echo t('price_range', 'نطاق السعر'); ?>
                        </label>
                        <select class="form-select" name="price_max">
                            <option value="1000000"><?php echo t('any_price', 'أي سعر'); ?></option>
                            <option value="100000" <?php echo $price_max === 100000 ? 'selected' : ''; ?>>100,000 <?php echo t('currency'); ?></option>
                            <option value="200000" <?php echo $price_max === 200000 ? 'selected' : ''; ?>>200,000 <?php echo t('currency'); ?></option>
                            <option value="300000" <?php echo $price_max === 300000 ? 'selected' : ''; ?>>300,000 <?php echo t('currency'); ?></option>
                            <option value="500000" <?php echo $price_max === 500000 ? 'selected' : ''; ?>>500,000 <?php echo t('currency'); ?></option>
                        </select>
                    </div>
                </div>
                
                <!-- Year -->
                <div class="col-lg-2 col-md-6">
                    <div class="form-group">
                        <label class="form-label fw-semibold">
                            <i class="fas fa-calendar me-1"></i><?php echo t('year'); ?>
                        </label>
                        <select class="form-select" name="year_min">
                            <option value="2020"><?php echo t('any_year', 'أي سنة'); ?></option>
                            <option value="2024" <?php echo $year_min === 2024 ? 'selected' : ''; ?>>2024</option>
                            <option value="2023" <?php echo $year_min === 2023 ? 'selected' : ''; ?>>2023</option>
                            <option value="2022" <?php echo $year_min === 2022 ? 'selected' : ''; ?>>2022</option>
                            <option value="2021" <?php echo $year_min === 2021 ? 'selected' : ''; ?>>2021</option>
                        </select>
                    </div>
                </div>
                
                <!-- Sort -->
                <div class="col-lg-2 col-md-6">
                    <div class="form-group">
                        <label class="form-label fw-semibold">
                            <i class="fas fa-sort me-1"></i><?php echo t('sort_by'); ?>
                        </label>
                        <select class="form-select" name="sort">
                            <option value="price_asc" <?php echo $sort_by === 'price_asc' ? 'selected' : ''; ?>><?php echo t('price_low_high'); ?></option>
                            <option value="price_desc" <?php echo $sort_by === 'price_desc' ? 'selected' : ''; ?>><?php echo t('price_high_low'); ?></option>
                            <option value="year_desc" <?php echo $sort_by === 'year_desc' ? 'selected' : ''; ?>><?php echo t('year_new_old'); ?></option>
                            <option value="year_asc" <?php echo $sort_by === 'year_asc' ? 'selected' : ''; ?>><?php echo t('year_old_new'); ?></option>
                        </select>
                    </div>
                </div>
                
                <!-- Filter Button -->
                <div class="col-lg-1 col-md-6 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-filter"></i>
                    </button>
                </div>
            </div>
        </form>
    </div>
</section>

<!-- Cars Grid -->
<section class="cars-section py-5">
    <div class="container">
        <?php if (empty($filtered_cars)): ?>
            <div class="text-center py-5" data-aos="fade-up">
                <i class="fas fa-car fa-5x text-muted mb-3"></i>
                <h3 class="text-muted"><?php echo t('no_cars_found', 'لم يتم العثور على سيارات'); ?></h3>
                <p class="text-muted"><?php echo t('try_different_filters', 'جرب فلاتر مختلفة'); ?></p>
                <a href="cars.php" class="btn btn-primary">
                    <i class="fas fa-refresh me-2"></i><?php echo t('reset_filters', 'إعادة تعيين الفلاتر'); ?>
                </a>
            </div>
        <?php else: ?>
            <div class="row" id="carsGrid">
                <?php foreach ($filtered_cars as $index => $car): ?>
                    <div class="col-lg-4 col-md-6 mb-4 car-item" data-aos="fade-up" data-aos-delay="<?php echo $index * 100; ?>">
                        <div class="card card-custom h-100">
                            <div class="card-img-container position-relative">
                                <img src="<?php echo $car['image']; ?>" class="card-img-top" alt="<?php echo htmlspecialchars($car['name']); ?>">
                                
                                <!-- Badges -->
                                <div class="card-badges">
                                    <?php if ($car['condition'] === 'new'): ?>
                                        <span class="badge bg-success"><?php echo t('new'); ?></span>
                                    <?php endif; ?>
                                    <?php if ($car['featured']): ?>
                                        <span class="badge bg-warning"><?php echo t('featured'); ?></span>
                                    <?php endif; ?>
                                </div>
                                
                                <!-- Overlay -->
                                <div class="card-overlay">
                                    <a href="car-details.php?id=<?php echo $car['id']; ?>" class="btn btn-primary btn-sm me-2">
                                        <i class="fas fa-eye me-1"></i><?php echo t('view_details'); ?>
                                    </a>
                                    <a href="contact.php?car=<?php echo $car['id']; ?>" class="btn btn-outline-light btn-sm">
                                        <i class="fas fa-phone me-1"></i><?php echo t('contact'); ?>
                                    </a>
                                </div>
                            </div>
                            
                            <div class="card-body">
                                <h5 class="card-title"><?php echo htmlspecialchars($car['name']); ?></h5>
                                
                                <div class="car-specs mb-3">
                                    <small class="text-muted d-block">
                                        <i class="fas fa-calendar me-1"></i><?php echo $car['year']; ?> • 
                                        <i class="fas fa-tachometer-alt me-1"></i><?php echo number_format($car['mileage']); ?> <?php echo t('km'); ?> • 
                                        <i class="fas fa-gas-pump me-1"></i><?php echo t($car['fuel_type']); ?>
                                    </small>
                                    <small class="text-muted d-block">
                                        <i class="fas fa-cogs me-1"></i><?php echo t($car['transmission']); ?> • 
                                        <i class="fas fa-star me-1"></i><?php echo t($car['condition']); ?>
                                    </small>
                                </div>
                                
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="price text-primary fw-bold fs-5">
                                        <?php echo number_format($car['price']); ?> <?php echo t('currency'); ?>
                                    </span>
                                    <div class="btn-group">
                                        <button class="btn btn-outline-primary btn-sm favorite-btn" data-car-id="<?php echo $car['id']; ?>">
                                            <i class="far fa-heart"></i>
                                        </button>
                                        <button class="btn btn-outline-primary btn-sm share-btn" data-car-id="<?php echo $car['id']; ?>">
                                            <i class="fas fa-share-alt"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
</section>

<?php include 'includes/footer.php'; ?>
