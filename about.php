<?php
require_once 'config.php';

$page_title = t('about');

include 'includes/header.php';
?>

<!-- Page Header -->
<section class="page-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="display-4 fw-bold mb-3" data-aos="fade-right">
                    <i class="fas fa-info-circle me-3"></i><?php echo t('about_title'); ?>
                </h1>
                <p class="lead mb-0" data-aos="fade-right" data-aos-delay="200">
                    <?php echo t('about_subtitle', 'تعرف على قصتنا ورؤيتنا في عالم السيارات الفاخرة'); ?>
                </p>
            </div>
            <div class="col-lg-4 text-lg-end" data-aos="fade-left" data-aos-delay="300">
                <div class="stats-summary">
                    <div class="stat-item">
                        <h3 class="fw-bold">10+</h3>
                        <p><?php echo t('years_experience', 'سنوات خبرة'); ?></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- About Content -->
<section class="about-content py-5">
    <div class="container">
        <div class="row align-items-center mb-5">
            <div class="col-lg-6 mb-4" data-aos="fade-right">
                <img src="https://via.placeholder.com/600x400/007bff/ffffff?text=About+Us" 
                     alt="About Us" class="img-fluid rounded shadow">
            </div>
            <div class="col-lg-6" data-aos="fade-left">
                <h2 class="mb-4"><?php echo t('our_story', 'قصتنا'); ?></h2>
                <p class="lead mb-4">
                    <?php echo t('story_intro', 'بدأت رحلتنا في عام 2014 برؤية واضحة: تقديم أفضل السيارات الفاخرة بأعلى معايير الجودة والخدمة.'); ?>
                </p>
                <p class="mb-4">
                    <?php echo t('story_content', 'على مدار السنوات، نمت شركتنا لتصبح واحدة من أبرز معارض السيارات في المملكة، حيث نقدم مجموعة متنوعة من أفخم السيارات العالمية مع خدمة عملاء استثنائية.'); ?>
                </p>
                <div class="row">
                    <div class="col-6">
                        <div class="stat-box text-center">
                            <h3 class="text-primary fw-bold">500+</h3>
                            <p class="mb-0"><?php echo t('happy_customers', 'عميل سعيد'); ?></p>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="stat-box text-center">
                            <h3 class="text-primary fw-bold">150+</h3>
                            <p class="mb-0"><?php echo t('cars_sold', 'سيارة مباعة'); ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Mission, Vision, Values -->
<section class="mvv-section py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-4 mb-4" data-aos="fade-up" data-aos-delay="100">
                <div class="mvv-card h-100">
                    <div class="mvv-icon">
                        <i class="fas fa-bullseye"></i>
                    </div>
                    <h3><?php echo t('our_mission'); ?></h3>
                    <p><?php echo t('mission_text', 'مهمتنا هي تقديم أفضل السيارات الفاخرة مع خدمة عملاء متميزة وأسعار تنافسية، لنكون الخيار الأول لعملائنا في رحلة البحث عن السيارة المثالية.'); ?></p>
                </div>
            </div>
            
            <div class="col-lg-4 mb-4" data-aos="fade-up" data-aos-delay="200">
                <div class="mvv-card h-100">
                    <div class="mvv-icon">
                        <i class="fas fa-eye"></i>
                    </div>
                    <h3><?php echo t('our_vision'); ?></h3>
                    <p><?php echo t('vision_text', 'رؤيتنا أن نكون المعرض الرائد في المملكة العربية السعودية لبيع السيارات الفاخرة، ونموذجاً يحتذى به في الجودة والخدمة والابتكار.'); ?></p>
                </div>
            </div>
            
            <div class="col-lg-4 mb-4" data-aos="fade-up" data-aos-delay="300">
                <div class="mvv-card h-100">
                    <div class="mvv-icon">
                        <i class="fas fa-heart"></i>
                    </div>
                    <h3><?php echo t('our_values'); ?></h3>
                    <p><?php echo t('values_text', 'نؤمن بالصدق والشفافية في التعامل، والتميز في الخدمة، والابتكار المستمر، واحترام عملائنا وتقدير ثقتهم بنا.'); ?></p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Team Section -->
<section class="team-section py-5">
    <div class="container">
        <div class="section-title" data-aos="fade-up">
            <h2><?php echo t('our_team', 'فريقنا'); ?></h2>
            <p><?php echo t('team_desc', 'تعرف على الفريق المتخصص الذي يعمل على خدمتكم'); ?></p>
        </div>
        
        <div class="row">
            <div class="col-lg-3 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="100">
                <div class="team-card">
                    <div class="team-image">
                        <img src="https://via.placeholder.com/300x300/007bff/ffffff?text=CEO" alt="CEO" class="img-fluid">
                        <div class="team-overlay">
                            <div class="social-links">
                                <a href="#"><i class="fab fa-linkedin-in"></i></a>
                                <a href="#"><i class="fab fa-twitter"></i></a>
                            </div>
                        </div>
                    </div>
                    <div class="team-info">
                        <h4><?php echo t('ceo_name', 'أحمد محمد'); ?></h4>
                        <p class="text-primary"><?php echo t('ceo_title', 'الرئيس التنفيذي'); ?></p>
                        <p class="text-muted"><?php echo t('ceo_desc', 'خبرة 15 عاماً في مجال السيارات'); ?></p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="200">
                <div class="team-card">
                    <div class="team-image">
                        <img src="https://via.placeholder.com/300x300/28a745/ffffff?text=Sales" alt="Sales Manager" class="img-fluid">
                        <div class="team-overlay">
                            <div class="social-links">
                                <a href="#"><i class="fab fa-linkedin-in"></i></a>
                                <a href="#"><i class="fab fa-twitter"></i></a>
                            </div>
                        </div>
                    </div>
                    <div class="team-info">
                        <h4><?php echo t('sales_manager_name', 'سارة أحمد'); ?></h4>
                        <p class="text-primary"><?php echo t('sales_manager_title', 'مدير المبيعات'); ?></p>
                        <p class="text-muted"><?php echo t('sales_manager_desc', 'متخصصة في خدمة العملاء'); ?></p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="300">
                <div class="team-card">
                    <div class="team-image">
                        <img src="https://via.placeholder.com/300x300/dc3545/ffffff?text=Tech" alt="Technical Manager" class="img-fluid">
                        <div class="team-overlay">
                            <div class="social-links">
                                <a href="#"><i class="fab fa-linkedin-in"></i></a>
                                <a href="#"><i class="fab fa-twitter"></i></a>
                            </div>
                        </div>
                    </div>
                    <div class="team-info">
                        <h4><?php echo t('tech_manager_name', 'محمد علي'); ?></h4>
                        <p class="text-primary"><?php echo t('tech_manager_title', 'المدير التقني'); ?></p>
                        <p class="text-muted"><?php echo t('tech_manager_desc', 'خبير في فحص السيارات'); ?></p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="400">
                <div class="team-card">
                    <div class="team-image">
                        <img src="https://via.placeholder.com/300x300/ffc107/ffffff?text=Finance" alt="Finance Manager" class="img-fluid">
                        <div class="team-overlay">
                            <div class="social-links">
                                <a href="#"><i class="fab fa-linkedin-in"></i></a>
                                <a href="#"><i class="fab fa-twitter"></i></a>
                            </div>
                        </div>
                    </div>
                    <div class="team-info">
                        <h4><?php echo t('finance_manager_name', 'فاطمة خالد'); ?></h4>
                        <p class="text-primary"><?php echo t('finance_manager_title', 'مدير التمويل'); ?></p>
                        <p class="text-muted"><?php echo t('finance_manager_desc', 'متخصصة في حلول التمويل'); ?></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Achievements Section -->
<section class="achievements-section py-5 bg-light">
    <div class="container">
        <div class="section-title" data-aos="fade-up">
            <h2><?php echo t('our_achievements', 'إنجازاتنا'); ?></h2>
            <p><?php echo t('achievements_desc', 'نفخر بما حققناه على مدار السنوات'); ?></p>
        </div>
        
        <div class="row">
            <div class="col-lg-3 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="100">
                <div class="achievement-card text-center">
                    <div class="achievement-icon">
                        <i class="fas fa-trophy"></i>
                    </div>
                    <h3 class="counter" data-target="5">0</h3>
                    <p><?php echo t('awards_won', 'جوائز حصلنا عليها'); ?></p>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="200">
                <div class="achievement-card text-center">
                    <div class="achievement-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3 class="counter" data-target="500">0</h3>
                    <p><?php echo t('satisfied_customers', 'عميل راضٍ'); ?></p>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="300">
                <div class="achievement-card text-center">
                    <div class="achievement-icon">
                        <i class="fas fa-car"></i>
                    </div>
                    <h3 class="counter" data-target="150">0</h3>
                    <p><?php echo t('cars_available', 'سيارة متاحة'); ?></p>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="400">
                <div class="achievement-card text-center">
                    <div class="achievement-icon">
                        <i class="fas fa-star"></i>
                    </div>
                    <h3 class="counter" data-target="98">0</h3>
                    <p><?php echo t('satisfaction_rate', '% نسبة الرضا'); ?></p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Testimonials Section -->
<section class="testimonials-section py-5">
    <div class="container">
        <div class="section-title" data-aos="fade-up">
            <h2><?php echo t('testimonials'); ?></h2>
            <p><?php echo t('testimonials_desc', 'ماذا يقول عملاؤنا عنا'); ?></p>
        </div>
        
        <div class="row">
            <div class="col-lg-4 mb-4" data-aos="fade-up" data-aos-delay="100">
                <div class="testimonial-card">
                    <div class="testimonial-content">
                        <div class="stars mb-3">
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                        </div>
                        <p>"<?php echo t('testimonial_1', 'خدمة ممتازة وسيارات بجودة عالية. أنصح بالتعامل معهم.'); ?>"</p>
                    </div>
                    <div class="testimonial-author">
                        <img src="https://via.placeholder.com/60x60/007bff/ffffff?text=A" alt="Customer" class="rounded-circle">
                        <div>
                            <h6><?php echo t('customer_1_name', 'عبدالله محمد'); ?></h6>
                            <small class="text-muted"><?php echo t('customer_1_title', 'رجل أعمال'); ?></small>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4 mb-4" data-aos="fade-up" data-aos-delay="200">
                <div class="testimonial-card">
                    <div class="testimonial-content">
                        <div class="stars mb-3">
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                        </div>
                        <p>"<?php echo t('testimonial_2', 'تعامل راقي وأسعار مناسبة. حصلت على السيارة التي أحلم بها.'); ?>"</p>
                    </div>
                    <div class="testimonial-author">
                        <img src="https://via.placeholder.com/60x60/28a745/ffffff?text=S" alt="Customer" class="rounded-circle">
                        <div>
                            <h6><?php echo t('customer_2_name', 'سعد أحمد'); ?></h6>
                            <small class="text-muted"><?php echo t('customer_2_title', 'مهندس'); ?></small>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4 mb-4" data-aos="fade-up" data-aos-delay="300">
                <div class="testimonial-card">
                    <div class="testimonial-content">
                        <div class="stars mb-3">
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                        </div>
                        <p>"<?php echo t('testimonial_3', 'فريق محترف ومتعاون. ساعدوني في اختيار السيارة المناسبة.'); ?>"</p>
                    </div>
                    <div class="testimonial-author">
                        <img src="https://via.placeholder.com/60x60/dc3545/ffffff?text=N" alt="Customer" class="rounded-circle">
                        <div>
                            <h6><?php echo t('customer_3_name', 'نورا خالد'); ?></h6>
                            <small class="text-muted"><?php echo t('customer_3_title', 'طبيبة'); ?></small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
// Counter animation
document.addEventListener('DOMContentLoaded', function() {
    const counters = document.querySelectorAll('.counter');
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const counter = entry.target;
                const target = parseInt(counter.dataset.target);
                let current = 0;
                const increment = target / 100;
                
                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        current = target;
                        clearInterval(timer);
                    }
                    counter.textContent = Math.floor(current);
                }, 20);
                
                observer.unobserve(counter);
            }
        });
    });
    
    counters.forEach(counter => observer.observe(counter));
});
</script>

<style>
.stats-summary {
    background: rgba(255,255,255,0.1);
    padding: 20px;
    border-radius: 10px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
    text-align: center;
}

.stats-summary h3 {
    color: white;
    margin-bottom: 5px;
}

.stats-summary p {
    color: rgba(255,255,255,0.8);
    margin-bottom: 0;
}

.stat-box {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
}

.mvv-card {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    text-align: center;
    transition: transform 0.3s ease;
}

.mvv-card:hover {
    transform: translateY(-10px);
}

.mvv-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
}

.mvv-icon i {
    font-size: 2rem;
    color: white;
}

.team-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.team-card:hover {
    transform: translateY(-10px);
}

.team-image {
    position: relative;
    overflow: hidden;
}

.team-image img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.team-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.team-card:hover .team-overlay {
    opacity: 1;
}

.team-card:hover .team-image img {
    transform: scale(1.1);
}

.social-links a {
    display: inline-block;
    width: 40px;
    height: 40px;
    background: white;
    color: #007bff;
    border-radius: 50%;
    text-decoration: none;
    margin: 0 5px;
    line-height: 40px;
    text-align: center;
    transition: transform 0.3s ease;
}

.social-links a:hover {
    transform: scale(1.1);
}

.team-info {
    padding: 20px;
    text-align: center;
}

.achievement-card {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.achievement-card:hover {
    transform: translateY(-10px);
}

.achievement-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
}

.achievement-icon i {
    font-size: 2rem;
    color: white;
}

.testimonial-card {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    height: 100%;
}

.testimonial-author {
    display: flex;
    align-items: center;
    margin-top: 20px;
}

.testimonial-author img {
    margin-right: 15px;
    width: 60px;
    height: 60px;
}

.testimonial-author div {
    flex: 1;
}
</style>

<?php include 'includes/footer.php'; ?>
