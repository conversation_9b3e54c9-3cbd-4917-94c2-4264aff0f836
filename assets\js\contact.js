// ===== Contact Page Specific JavaScript =====

document.addEventListener('DOMContentLoaded', function() {
    
    // Initialize contact form functionality
    initContactForm();
    initPhoneValidation();
    initFormAnimations();
    initMapInteraction();
    
});

function initContactForm() {
    const contactForm = document.getElementById('contactForm');
    const submitBtn = document.getElementById('submitBtn');
    
    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            if (this.checkValidity()) {
                // Show loading state
                showLoadingState(submitBtn);
                
                // Simulate form submission delay
                setTimeout(() => {
                    // In real application, this would be an AJAX request
                    this.submit();
                }, 1000);
            }
            
            this.classList.add('was-validated');
        });
        
        // Real-time validation
        const inputs = contactForm.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            input.addEventListener('blur', function() {
                validateField(this);
            });
            
            input.addEventListener('input', function() {
                if (this.classList.contains('is-invalid')) {
                    validateField(this);
                }
            });
        });
    }
}

function initPhoneValidation() {
    const phoneInput = document.getElementById('phone');
    
    if (phoneInput) {
        phoneInput.addEventListener('input', function() {
            // Remove non-numeric characters except + and spaces
            let value = this.value.replace(/[^\d+\s-]/g, '');
            
            // Format phone number
            if (value.startsWith('966')) {
                value = '+' + value;
            } else if (value.startsWith('05') && value.length === 10) {
                value = '+966' + value.substring(1);
            }
            
            this.value = value;
            
            // Validate phone number
            const phoneRegex = /^(\+966|0)(5|1)[0-9]{8}$/;
            if (phoneRegex.test(value.replace(/\s/g, ''))) {
                this.setCustomValidity('');
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            } else if (value.length > 0) {
                this.setCustomValidity('رقم الهاتف غير صحيح');
                this.classList.remove('is-valid');
                this.classList.add('is-invalid');
            }
        });
    }
}

function initFormAnimations() {
    const formGroups = document.querySelectorAll('.contact-form .mb-3');
    
    formGroups.forEach((group, index) => {
        group.style.opacity = '0';
        group.style.transform = 'translateY(20px)';
        group.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
        
        setTimeout(() => {
            group.style.opacity = '1';
            group.style.transform = 'translateY(0)';
        }, index * 100);
    });
    
    // Floating label effect
    const inputs = document.querySelectorAll('.form-control, .form-select');
    inputs.forEach(input => {
        const label = input.previousElementSibling;
        
        if (label && label.tagName === 'LABEL') {
            input.addEventListener('focus', function() {
                label.style.transform = 'translateY(-25px) scale(0.85)';
                label.style.color = '#007bff';
            });
            
            input.addEventListener('blur', function() {
                if (!this.value) {
                    label.style.transform = 'translateY(0) scale(1)';
                    label.style.color = '';
                }
            });
            
            // Check if input has value on load
            if (input.value) {
                label.style.transform = 'translateY(-25px) scale(0.85)';
            }
        }
    });
}

function initMapInteraction() {
    const mapContainer = document.querySelector('.map-container');
    const mapIframe = mapContainer?.querySelector('iframe');
    
    if (mapContainer && mapIframe) {
        // Add overlay to prevent scrolling interference
        const overlay = document.createElement('div');
        overlay.className = 'map-overlay';
        overlay.innerHTML = '<p>انقر لتفعيل الخريطة</p>';
        mapContainer.appendChild(overlay);
        
        overlay.addEventListener('click', function() {
            this.style.display = 'none';
            mapIframe.style.pointerEvents = 'auto';
        });
        
        // Reset overlay when clicking outside
        document.addEventListener('click', function(e) {
            if (!mapContainer.contains(e.target)) {
                overlay.style.display = 'flex';
                mapIframe.style.pointerEvents = 'none';
            }
        });
    }
}

function validateField(field) {
    const fieldType = field.type || field.tagName.toLowerCase();
    let isValid = true;
    let errorMessage = '';
    
    // Check if field is required and empty
    if (field.hasAttribute('required') && !field.value.trim()) {
        isValid = false;
        errorMessage = 'هذا الحقل مطلوب';
    }
    
    // Specific validations
    switch (fieldType) {
        case 'email':
            if (field.value && !isValidEmail(field.value)) {
                isValid = false;
                errorMessage = 'بريد إلكتروني غير صحيح';
            }
            break;
            
        case 'tel':
            if (field.value && !isValidPhone(field.value)) {
                isValid = false;
                errorMessage = 'رقم هاتف غير صحيح';
            }
            break;
            
        case 'textarea':
            if (field.value && field.value.length < 10) {
                isValid = false;
                errorMessage = 'الرسالة قصيرة جداً (10 أحرف على الأقل)';
            }
            break;
    }
    
    // Update field state
    if (isValid) {
        field.classList.remove('is-invalid');
        field.classList.add('is-valid');
        field.setCustomValidity('');
    } else {
        field.classList.remove('is-valid');
        field.classList.add('is-invalid');
        field.setCustomValidity(errorMessage);
    }
    
    return isValid;
}

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function isValidPhone(phone) {
    const phoneRegex = /^(\+966|0)(5|1)[0-9]{8}$/;
    return phoneRegex.test(phone.replace(/\s/g, ''));
}

function showLoadingState(button) {
    const spinner = button.querySelector('.spinner-border');
    const icon = button.querySelector('.fas');
    
    if (spinner && icon) {
        spinner.classList.remove('d-none');
        icon.style.display = 'none';
        button.disabled = true;
        button.style.opacity = '0.7';
    }
}

// Character counter for textarea
const messageTextarea = document.getElementById('message');
if (messageTextarea) {
    const maxLength = 500;
    const counter = document.createElement('div');
    counter.className = 'character-counter text-muted small mt-1';
    messageTextarea.parentNode.appendChild(counter);
    
    function updateCounter() {
        const remaining = maxLength - messageTextarea.value.length;
        counter.textContent = `${messageTextarea.value.length}/${maxLength} حرف`;
        
        if (remaining < 50) {
            counter.classList.add('text-warning');
        } else {
            counter.classList.remove('text-warning');
        }
        
        if (remaining < 0) {
            counter.classList.add('text-danger');
            messageTextarea.setCustomValidity('الرسالة طويلة جداً');
        } else {
            counter.classList.remove('text-danger');
            messageTextarea.setCustomValidity('');
        }
    }
    
    messageTextarea.addEventListener('input', updateCounter);
    updateCounter(); // Initial call
}

// Auto-resize textarea
if (messageTextarea) {
    messageTextarea.addEventListener('input', function() {
        this.style.height = 'auto';
        this.style.height = (this.scrollHeight) + 'px';
    });
}

// Add custom styles
const style = document.createElement('style');
style.textContent = `
    .contact-hours {
        background: rgba(255,255,255,0.1);
        padding: 20px;
        border-radius: 10px;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255,255,255,0.2);
    }
    
    .contact-info .contact-item {
        margin-bottom: 25px;
        padding: 15px 0;
        border-bottom: 1px solid rgba(255,255,255,0.1);
    }
    
    .contact-info .contact-item:last-child {
        border-bottom: none;
    }
    
    .contact-info .contact-item i {
        margin-right: 15px;
        margin-left: 0;
    }
    
    .contact-info .contact-item h5 {
        margin-bottom: 5px;
        color: white;
    }
    
    .contact-info .contact-item p {
        margin-bottom: 0;
        color: rgba(255,255,255,0.8);
    }
    
    .social-links {
        text-align: center;
    }
    
    .social-link {
        display: inline-block;
        width: 40px;
        height: 40px;
        background: rgba(255,255,255,0.2);
        color: white;
        border-radius: 50%;
        text-decoration: none;
        margin: 0 5px;
        line-height: 40px;
        transition: all 0.3s ease;
    }
    
    .social-link:hover {
        background: white;
        color: #007bff;
        transform: translateY(-3px);
    }
    
    .quick-contact {
        background: white;
        padding: 25px;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    
    .map-container {
        position: relative;
        overflow: hidden;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    
    .map-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0,0,0,0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.2rem;
        cursor: pointer;
        z-index: 10;
        transition: opacity 0.3s ease;
    }
    
    .map-overlay:hover {
        background: rgba(0,0,0,0.7);
    }
    
    .map-container iframe {
        pointer-events: none;
        transition: filter 0.3s ease;
    }
    
    .form-control:focus,
    .form-select:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }
    
    .form-control.is-valid,
    .form-select.is-valid {
        border-color: #28a745;
    }
    
    .form-control.is-invalid,
    .form-select.is-invalid {
        border-color: #dc3545;
    }
    
    .character-counter {
        text-align: right;
    }
    
    .accordion-button:not(.collapsed) {
        background-color: #007bff;
        color: white;
    }
    
    .accordion-button:focus {
        box-shadow: 0 0 0 0.25rem rgba(0, 123, 255, 0.25);
    }
    
    @media (max-width: 768px) {
        .contact-info .contact-item i {
            margin-right: 10px;
        }
        
        .social-link {
            width: 35px;
            height: 35px;
            line-height: 35px;
            margin: 0 3px;
        }
    }
`;
document.head.appendChild(style);
