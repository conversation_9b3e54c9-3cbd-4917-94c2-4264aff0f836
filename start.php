<?php
/**
 * ملف التشغيل السريع لموقع معرض السيارات
 * Quick Start File for Car Showroom Website
 */

// التحقق من متطلبات النظام
echo "<h1>🚗 Car Showroom - Quick Start</h1>";
echo "<h2>فحص متطلبات النظام | System Requirements Check</h2>";

$requirements = [
    'PHP Version >= 8.0' => version_compare(PHP_VERSION, '8.0.0', '>='),
    'PDO Extension' => extension_loaded('pdo'),
    'PDO MySQL Extension' => extension_loaded('pdo_mysql'),
    'JSON Extension' => extension_loaded('json'),
    'Session Support' => function_exists('session_start'),
    'File Upload Support' => ini_get('file_uploads'),
    'GD Extension (for images)' => extension_loaded('gd'),
    'cURL Extension' => extension_loaded('curl'),
    'OpenSSL Extension' => extension_loaded('openssl'),
];

$allPassed = true;

echo "<table border='1' cellpadding='10' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Requirement</th><th>Status</th></tr>";

foreach ($requirements as $requirement => $status) {
    $statusText = $status ? '✅ Passed' : '❌ Failed';
    $color = $status ? 'green' : 'red';
    echo "<tr><td>$requirement</td><td style='color: $color;'>$statusText</td></tr>";
    
    if (!$status) {
        $allPassed = false;
    }
}

echo "</table>";

// معلومات إضافية
echo "<h2>معلومات النظام | System Information</h2>";
echo "<table border='1' cellpadding='10' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Setting</th><th>Value</th></tr>";

$systemInfo = [
    'PHP Version' => PHP_VERSION,
    'Server Software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
    'Document Root' => $_SERVER['DOCUMENT_ROOT'] ?? 'Unknown',
    'Current Directory' => __DIR__,
    'Memory Limit' => ini_get('memory_limit'),
    'Max Execution Time' => ini_get('max_execution_time') . ' seconds',
    'Upload Max Filesize' => ini_get('upload_max_filesize'),
    'Post Max Size' => ini_get('post_max_size'),
    'Session Save Path' => session_save_path() ?: 'Default',
    'Timezone' => date_default_timezone_get(),
];

foreach ($systemInfo as $setting => $value) {
    echo "<tr><td>$setting</td><td>$value</td></tr>";
}

echo "</table>";

// التحقق من الملفات المطلوبة
echo "<h2>فحص الملفات | File Check</h2>";

$requiredFiles = [
    'config.php' => 'Configuration file',
    'index.php' => 'Homepage',
    'cars.php' => 'Cars listing page',
    'car-details.php' => 'Car details page',
    'contact.php' => 'Contact page',
    'about.php' => 'About page',
    'database.sql' => 'Database structure',
    'assets/css/style.css' => 'Main stylesheet',
    'assets/js/main.js' => 'Main JavaScript',
    'includes/header.php' => 'Header template',
    'includes/footer.php' => 'Footer template',
    'lang/ar/translations.php' => 'Arabic translations',
    'lang/en/translations.php' => 'English translations',
];

echo "<table border='1' cellpadding='10' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>File</th><th>Description</th><th>Status</th></tr>";

foreach ($requiredFiles as $file => $description) {
    $exists = file_exists($file);
    $statusText = $exists ? '✅ Found' : '❌ Missing';
    $color = $exists ? 'green' : 'red';
    echo "<tr><td>$file</td><td>$description</td><td style='color: $color;'>$statusText</td></tr>";
    
    if (!$exists) {
        $allPassed = false;
    }
}

echo "</table>";

// التحقق من صلاحيات المجلدات
echo "<h2>فحص الصلاحيات | Permissions Check</h2>";

$directories = [
    'assets/images' => 'Images directory',
    'assets/images/cars' => 'Car images directory',
    'assets/images/brands' => 'Brand logos directory',
];

echo "<table border='1' cellpadding='10' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Directory</th><th>Description</th><th>Writable</th></tr>";

foreach ($directories as $dir => $description) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
    
    $writable = is_writable($dir);
    $statusText = $writable ? '✅ Writable' : '❌ Not Writable';
    $color = $writable ? 'green' : 'red';
    echo "<tr><td>$dir</td><td>$description</td><td style='color: $color;'>$statusText</td></tr>";
}

echo "</table>";

// اختبار الاتصال بقاعدة البيانات
echo "<h2>اختبار قاعدة البيانات | Database Test</h2>";

if (file_exists('config.php')) {
    try {
        require_once 'config.php';
        
        $pdo = new PDO(
            "mysql:host=" . DB_HOST . ";charset=utf8mb4",
            DB_USER,
            DB_PASS,
            [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            ]
        );
        
        echo "<p style='color: green;'>✅ Database connection successful!</p>";
        
        // التحقق من وجود قاعدة البيانات
        $stmt = $pdo->query("SHOW DATABASES LIKE '" . DB_NAME . "'");
        if ($stmt->rowCount() > 0) {
            echo "<p style='color: green;'>✅ Database '" . DB_NAME . "' exists!</p>";
            
            // التحقق من الجداول
            $pdo->exec("USE " . DB_NAME);
            $stmt = $pdo->query("SHOW TABLES");
            $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            if (count($tables) > 0) {
                echo "<p style='color: green;'>✅ Found " . count($tables) . " tables in database</p>";
                echo "<ul>";
                foreach ($tables as $table) {
                    echo "<li>$table</li>";
                }
                echo "</ul>";
            } else {
                echo "<p style='color: orange;'>⚠️ Database exists but no tables found. Please import database.sql</p>";
            }
        } else {
            echo "<p style='color: orange;'>⚠️ Database '" . DB_NAME . "' does not exist. Please create it and import database.sql</p>";
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ Database connection failed: " . $e->getMessage() . "</p>";
        echo "<p>Please check your database configuration in config.php</p>";
        $allPassed = false;
    }
} else {
    echo "<p style='color: red;'>❌ config.php file not found!</p>";
    $allPassed = false;
}

// النتيجة النهائية
echo "<h2>النتيجة النهائية | Final Result</h2>";

if ($allPassed) {
    echo "<div style='background: #d4edda; color: #155724; padding: 20px; border: 1px solid #c3e6cb; border-radius: 5px;'>";
    echo "<h3>🎉 تهانينا! | Congratulations!</h3>";
    echo "<p>جميع المتطلبات متوفرة والموقع جاهز للعمل | All requirements are met and the website is ready to run!</p>";
    echo "<p><strong>الخطوات التالية | Next Steps:</strong></p>";
    echo "<ol>";
    echo "<li>إنشاء قاعدة البيانات واستيراد ملف database.sql | Create database and import database.sql</li>";
    echo "<li>تحديث إعدادات قاعدة البيانات في config.php | Update database settings in config.php</li>";
    echo "<li>زيارة الموقع: <a href='index.php' target='_blank'>index.php</a></li>";
    echo "</ol>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; border: 1px solid #f5c6cb; border-radius: 5px;'>";
    echo "<h3>⚠️ تحذير | Warning!</h3>";
    echo "<p>بعض المتطلبات غير متوفرة. يرجى إصلاح المشاكل المذكورة أعلاه | Some requirements are not met. Please fix the issues mentioned above.</p>";
    echo "</div>";
}

// روابط مفيدة
echo "<h2>روابط مفيدة | Useful Links</h2>";
echo "<ul>";
echo "<li><a href='index.php' target='_blank'>الصفحة الرئيسية | Homepage</a></li>";
echo "<li><a href='cars.php' target='_blank'>صفحة السيارات | Cars Page</a></li>";
echo "<li><a href='contact.php' target='_blank'>صفحة الاتصال | Contact Page</a></li>";
echo "<li><a href='about.php' target='_blank'>صفحة من نحن | About Page</a></li>";
echo "<li><a href='README.md' target='_blank'>دليل التثبيت | Installation Guide</a></li>";
echo "</ul>";

// معلومات إضافية
echo "<h2>معلومات إضافية | Additional Information</h2>";
echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 5px;'>";
echo "<h4>🔧 إعداد قاعدة البيانات | Database Setup</h4>";
echo "<pre>";
echo "1. إنشاء قاعدة البيانات | Create Database:\n";
echo "   CREATE DATABASE car_showroom CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;\n\n";
echo "2. استيراد البيانات | Import Data:\n";
echo "   mysql -u username -p car_showroom < database.sql\n\n";
echo "3. تحديث config.php | Update config.php:\n";
echo "   define('DB_HOST', 'localhost');\n";
echo "   define('DB_NAME', 'car_showroom');\n";
echo "   define('DB_USER', 'your_username');\n";
echo "   define('DB_PASS', 'your_password');";
echo "</pre>";
echo "</div>";

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin-top: 15px;'>";
echo "<h4>📁 هيكل المشروع | Project Structure</h4>";
echo "<pre>";
echo "car-showroom/\n";
echo "├── assets/          # الملفات الثابتة | Static files\n";
echo "├── includes/        # ملفات PHP المشتركة | Shared PHP files\n";
echo "├── lang/           # ملفات الترجمة | Translation files\n";
echo "├── config.php      # إعدادات النظام | System configuration\n";
echo "├── index.php       # الصفحة الرئيسية | Homepage\n";
echo "├── cars.php        # صفحة السيارات | Cars page\n";
echo "├── contact.php     # صفحة الاتصال | Contact page\n";
echo "├── about.php       # صفحة من نحن | About page\n";
echo "└── database.sql    # قاعدة البيانات | Database structure";
echo "</pre>";
echo "</div>";

echo "<hr>";
echo "<p style='text-align: center; color: #666;'>";
echo "Car Showroom Website v1.0 | تم التطوير بواسطة Augment Agent<br>";
echo "للدعم التقني، يرجى مراجعة ملف README.md | For technical support, please refer to README.md";
echo "</p>";
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 20px;
    background: #f8f9fa;
    line-height: 1.6;
}

h1, h2 {
    color: #333;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
}

table {
    background: white;
    margin: 15px 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

th {
    background: #007bff;
    color: white;
    padding: 12px;
}

td {
    padding: 10px;
}

tr:nth-child(even) {
    background: #f8f9fa;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

pre {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 4px;
    overflow-x: auto;
}
</style>
