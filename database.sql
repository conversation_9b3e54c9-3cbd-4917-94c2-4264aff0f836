-- إنشاء قاعدة البيانات
CREATE DATABASE IF NOT EXISTS car_showroom CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE car_showroom;

-- جدول الماركات
CREATE TABLE brands (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    name_en VARCHAR(100) NOT NULL,
    logo VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول السيارات
CREATE TABLE cars (
    id INT PRIMARY KEY AUTO_INCREMENT,
    brand_id INT,
    name VA<PERSON><PERSON><PERSON>(200) NOT NULL,
    name_en VARCHAR(200) NOT NULL,
    model VARCHAR(100) NOT NULL,
    year INT NOT NULL,
    price DECIMAL(12,2) NOT NULL,
    mileage INT DEFAULT 0,
    fuel_type ENUM('petrol', 'diesel', 'hybrid', 'electric') NOT NULL,
    transmission ENUM('manual', 'automatic') NOT NULL,
    engine VARCHAR(100),
    color VARCHAR(100),
    color_en VARCHAR(100),
    condition_status ENUM('new', 'excellent', 'good', 'fair') NOT NULL,
    location VARCHAR(100),
    location_en VARCHAR(100),
    description TEXT,
    description_en TEXT,
    features JSON,
    features_en JSON,
    is_featured BOOLEAN DEFAULT FALSE,
    is_sold BOOLEAN DEFAULT FALSE,
    views_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (brand_id) REFERENCES brands(id) ON DELETE SET NULL,
    INDEX idx_brand (brand_id),
    INDEX idx_price (price),
    INDEX idx_year (year),
    INDEX idx_featured (is_featured),
    INDEX idx_sold (is_sold)
);

-- جدول صور السيارات
CREATE TABLE car_images (
    id INT PRIMARY KEY AUTO_INCREMENT,
    car_id INT NOT NULL,
    image_path VARCHAR(255) NOT NULL,
    is_main BOOLEAN DEFAULT FALSE,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (car_id) REFERENCES cars(id) ON DELETE CASCADE,
    INDEX idx_car (car_id),
    INDEX idx_main (is_main)
);

-- جدول رسائل الاتصال
CREATE TABLE contact_messages (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(150) NOT NULL,
    phone VARCHAR(20),
    subject VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    car_id INT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (car_id) REFERENCES cars(id) ON DELETE SET NULL,
    INDEX idx_read (is_read),
    INDEX idx_car (car_id),
    INDEX idx_created (created_at)
);

-- جدول المفضلة (للمستخدمين المسجلين مستقبلاً)
CREATE TABLE favorites (
    id INT PRIMARY KEY AUTO_INCREMENT,
    session_id VARCHAR(128),
    car_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (car_id) REFERENCES cars(id) ON DELETE CASCADE,
    UNIQUE KEY unique_session_car (session_id, car_id),
    INDEX idx_session (session_id),
    INDEX idx_car (car_id)
);

-- جدول الإعدادات
CREATE TABLE settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    setting_value_en TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- إدراج بيانات الماركات
INSERT INTO brands (name, name_en, logo) VALUES
('بي إم دبليو', 'BMW', 'bmw-logo.png'),
('مرسيدس بنز', 'Mercedes-Benz', 'mercedes-logo.png'),
('أودي', 'Audi', 'audi-logo.png'),
('تويوتا', 'Toyota', 'toyota-logo.png'),
('لكزس', 'Lexus', 'lexus-logo.png'),
('بورش', 'Porsche', 'porsche-logo.png'),
('جاكوار', 'Jaguar', 'jaguar-logo.png'),
('لاند روفر', 'Land Rover', 'landrover-logo.png');

-- إدراج بيانات السيارات التجريبية
INSERT INTO cars (brand_id, name, name_en, model, year, price, mileage, fuel_type, transmission, engine, color, color_en, condition_status, location, location_en, description, description_en, features, features_en, is_featured) VALUES
(1, 'بي إم دبليو X5 2024', 'BMW X5 2024', 'X5', 2024, 450000.00, 0, 'petrol', 'automatic', '3.0L Twin Turbo', 'أسود معدني', 'Metallic Black', 'new', 'الرياض', 'Riyadh', 'بي إم دبليو X5 2024 الجديدة كلياً بمحرك قوي وتصميم أنيق', 'Brand new BMW X5 2024 with powerful engine and elegant design', '["نظام الملاحة GPS", "كاميرا خلفية", "مقاعد جلدية", "نظام صوتي متطور"]', '["GPS Navigation", "Rear Camera", "Leather Seats", "Premium Sound System"]', TRUE),

(2, 'مرسيدس C-Class 2023', 'Mercedes C-Class 2023', 'C-Class', 2023, 320000.00, 15000, 'petrol', 'automatic', '2.0L Turbo', 'أبيض لؤلؤي', 'Pearl White', 'excellent', 'جدة', 'Jeddah', 'مرسيدس C-Class 2023 بحالة ممتازة وصيانة دورية', 'Mercedes C-Class 2023 in excellent condition with regular maintenance', '["نظام الملاحة", "كاميرا 360 درجة", "مقاعد جلدية مدفأة", "نظام صوتي Burmester"]', '["Navigation System", "360 Camera", "Heated Leather Seats", "Burmester Sound System"]', TRUE),

(3, 'أودي A6 2023', 'Audi A6 2023', 'A6', 2023, 280000.00, 8500, 'petrol', 'automatic', '2.0L TFSI', 'رمادي', 'Gray', 'excellent', 'الدمام', 'Dammam', 'أودي A6 2023 بتقنيات متقدمة وأداء رائع', 'Audi A6 2023 with advanced technology and excellent performance', '["نظام الملاحة MMI", "كاميرا خلفية", "مقاعد رياضية", "إضاءة LED"]', '["MMI Navigation", "Rear Camera", "Sport Seats", "LED Lighting"]', FALSE),

(4, 'تويوتا كامري 2023', 'Toyota Camry 2023', 'Camry', 2023, 150000.00, 25000, 'petrol', 'automatic', '2.5L', 'فضي', 'Silver', 'good', 'الرياض', 'Riyadh', 'تويوتا كامري 2023 موثوقة واقتصادية', 'Toyota Camry 2023 reliable and economical', '["نظام الأمان Toyota Safety Sense", "شاشة لمس", "كاميرا خلفية", "مثبت سرعة"]', '["Toyota Safety Sense", "Touch Screen", "Rear Camera", "Cruise Control"]', FALSE),

(5, 'لكزس ES 2022', 'Lexus ES 2022', 'ES', 2022, 220000.00, 35000, 'hybrid', 'automatic', '2.5L Hybrid', 'أزرق داكن', 'Dark Blue', 'good', 'الرياض', 'Riyadh', 'لكزس ES 2022 هايبرد بتوفير ممتاز في الوقود', 'Lexus ES 2022 Hybrid with excellent fuel economy', '["نظام هايبرد", "مقاعد جلدية", "نظام صوتي Mark Levinson", "فتحة سقف"]', '["Hybrid System", "Leather Seats", "Mark Levinson Audio", "Sunroof"]', FALSE),

(6, 'بورش ماكان 2024', 'Porsche Macan 2024', 'Macan', 2024, 380000.00, 0, 'petrol', 'automatic', '2.0L Turbo', 'أحمر', 'Red', 'new', 'جدة', 'Jeddah', 'بورش ماكان 2024 الجديدة بأداء رياضي استثنائي', 'New Porsche Macan 2024 with exceptional sports performance', '["نظام الدفع الرباعي", "مقاعد رياضية", "نظام صوتي Bose", "عجلات رياضية"]', '["All-Wheel Drive", "Sport Seats", "Bose Audio", "Sport Wheels"]', TRUE);

-- إدراج صور السيارات
INSERT INTO car_images (car_id, image_path, is_main, sort_order) VALUES
(1, 'cars/bmw-x5-2024-main.jpg', TRUE, 1),
(1, 'cars/bmw-x5-2024-interior.jpg', FALSE, 2),
(1, 'cars/bmw-x5-2024-side.jpg', FALSE, 3),
(1, 'cars/bmw-x5-2024-back.jpg', FALSE, 4),

(2, 'cars/mercedes-c-class-2023-main.jpg', TRUE, 1),
(2, 'cars/mercedes-c-class-2023-interior.jpg', FALSE, 2),
(2, 'cars/mercedes-c-class-2023-side.jpg', FALSE, 3),

(3, 'cars/audi-a6-2023-main.jpg', TRUE, 1),
(3, 'cars/audi-a6-2023-interior.jpg', FALSE, 2),

(4, 'cars/toyota-camry-2023-main.jpg', TRUE, 1),
(4, 'cars/toyota-camry-2023-interior.jpg', FALSE, 2),

(5, 'cars/lexus-es-2022-main.jpg', TRUE, 1),
(5, 'cars/lexus-es-2022-interior.jpg', FALSE, 2),

(6, 'cars/porsche-macan-2024-main.jpg', TRUE, 1),
(6, 'cars/porsche-macan-2024-interior.jpg', FALSE, 2),
(6, 'cars/porsche-macan-2024-side.jpg', FALSE, 3);

-- إدراج الإعدادات
INSERT INTO settings (setting_key, setting_value, setting_value_en) VALUES
('site_name', 'معرض السيارات الفاخرة', 'Luxury Car Showroom'),
('site_description', 'معرض متخصص في بيع أفخم السيارات', 'Specialized showroom for luxury cars'),
('contact_phone', '+966 12 345 6789', '+966 12 345 6789'),
('contact_email', '<EMAIL>', '<EMAIL>'),
('contact_address', 'شارع الملك فهد، الرياض، المملكة العربية السعودية', 'King Fahd Road, Riyadh, Saudi Arabia'),
('working_hours', 'السبت - الخميس: 9:00 ص - 10:00 م', 'Sat - Thu: 9:00 AM - 10:00 PM'),
('facebook_url', 'https://facebook.com/carshowroom', 'https://facebook.com/carshowroom'),
('twitter_url', 'https://twitter.com/carshowroom', 'https://twitter.com/carshowroom'),
('instagram_url', 'https://instagram.com/carshowroom', 'https://instagram.com/carshowroom'),
('whatsapp_number', '+966123456789', '+966123456789');

-- إنشاء فهارس إضافية لتحسين الأداء
CREATE INDEX idx_cars_search ON cars(name, name_en, model);
CREATE INDEX idx_cars_price_year ON cars(price, year);
CREATE INDEX idx_cars_featured_sold ON cars(is_featured, is_sold);
CREATE INDEX idx_contact_messages_date ON contact_messages(created_at DESC);

-- إنشاء view للسيارات مع معلومات الماركة
CREATE VIEW cars_with_brands AS
SELECT 
    c.*,
    b.name as brand_name,
    b.name_en as brand_name_en,
    b.logo as brand_logo,
    (SELECT image_path FROM car_images WHERE car_id = c.id AND is_main = TRUE LIMIT 1) as main_image
FROM cars c
LEFT JOIN brands b ON c.brand_id = b.id
WHERE c.is_sold = FALSE;

-- إنشاء stored procedure للبحث المتقدم
DELIMITER //
CREATE PROCEDURE SearchCars(
    IN search_term VARCHAR(255),
    IN brand_filter INT,
    IN min_price DECIMAL(12,2),
    IN max_price DECIMAL(12,2),
    IN min_year INT,
    IN condition_filter VARCHAR(20),
    IN sort_by VARCHAR(20),
    IN page_limit INT,
    IN page_offset INT
)
BEGIN
    SET @sql = 'SELECT * FROM cars_with_brands WHERE is_sold = FALSE';
    
    IF search_term IS NOT NULL AND search_term != '' THEN
        SET @sql = CONCAT(@sql, ' AND (name LIKE "%', search_term, '%" OR name_en LIKE "%', search_term, '%" OR model LIKE "%', search_term, '%")');
    END IF;
    
    IF brand_filter IS NOT NULL AND brand_filter > 0 THEN
        SET @sql = CONCAT(@sql, ' AND brand_id = ', brand_filter);
    END IF;
    
    IF min_price IS NOT NULL AND min_price > 0 THEN
        SET @sql = CONCAT(@sql, ' AND price >= ', min_price);
    END IF;
    
    IF max_price IS NOT NULL AND max_price > 0 THEN
        SET @sql = CONCAT(@sql, ' AND price <= ', max_price);
    END IF;
    
    IF min_year IS NOT NULL AND min_year > 0 THEN
        SET @sql = CONCAT(@sql, ' AND year >= ', min_year);
    END IF;
    
    IF condition_filter IS NOT NULL AND condition_filter != '' THEN
        SET @sql = CONCAT(@sql, ' AND condition_status = "', condition_filter, '"');
    END IF;
    
    -- إضافة الترتيب
    IF sort_by = 'price_asc' THEN
        SET @sql = CONCAT(@sql, ' ORDER BY price ASC');
    ELSEIF sort_by = 'price_desc' THEN
        SET @sql = CONCAT(@sql, ' ORDER BY price DESC');
    ELSEIF sort_by = 'year_desc' THEN
        SET @sql = CONCAT(@sql, ' ORDER BY year DESC');
    ELSEIF sort_by = 'year_asc' THEN
        SET @sql = CONCAT(@sql, ' ORDER BY year ASC');
    ELSE
        SET @sql = CONCAT(@sql, ' ORDER BY is_featured DESC, created_at DESC');
    END IF;
    
    -- إضافة التصفح
    IF page_limit IS NOT NULL AND page_limit > 0 THEN
        SET @sql = CONCAT(@sql, ' LIMIT ', page_limit);
        IF page_offset IS NOT NULL AND page_offset > 0 THEN
            SET @sql = CONCAT(@sql, ' OFFSET ', page_offset);
        END IF;
    END IF;
    
    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
END //
DELIMITER ;

-- إنشاء trigger لتحديث عداد المشاهدات
DELIMITER //
CREATE TRIGGER update_car_views 
AFTER INSERT ON car_images 
FOR EACH ROW
BEGIN
    UPDATE cars SET views_count = views_count + 1 WHERE id = NEW.car_id;
END //
DELIMITER ;
