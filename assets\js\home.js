// ===== Home Page Specific JavaScript =====

document.addEventListener('DOMContentLoaded', function() {
    
    // Hero section animations
    initHeroAnimations();
    
    // Car cards hover effects
    initCarCardEffects();
    
    // Brand logos animation
    initBrandLogosAnimation();
    
    // Feature boxes animation
    initFeatureBoxesAnimation();
    
    // Floating elements animation
    initFloatingElements();
    
    // Statistics counter animation
    initStatsCounter();
});

function initHeroAnimations() {
    const heroTitle = document.querySelector('.hero-title');
    const heroSubtitle = document.querySelector('.hero-subtitle');
    const heroButtons = document.querySelector('.hero-buttons');
    
    if (heroTitle) {
        // Add typing effect to hero title
        const titleText = heroTitle.textContent;
        heroTitle.textContent = '';
        heroTitle.style.borderRight = '2px solid white';
        
        let i = 0;
        const typeWriter = () => {
            if (i < titleText.length) {
                heroTitle.textContent += titleText.charAt(i);
                i++;
                setTimeout(typeWriter, 100);
            } else {
                heroTitle.style.borderRight = 'none';
                // Animate subtitle after title is complete
                if (heroSubtitle) {
                    heroSubtitle.style.animation = 'fadeInUp 1s ease forwards';
                }
                // Animate buttons after subtitle
                setTimeout(() => {
                    if (heroButtons) {
                        heroButtons.style.animation = 'fadeInUp 1s ease forwards';
                    }
                }, 500);
            }
        };
        
        setTimeout(typeWriter, 1000);
    }
}

function initCarCardEffects() {
    const carCards = document.querySelectorAll('.card-custom');
    
    carCards.forEach(card => {
        const cardImg = card.querySelector('.card-img-top');
        const cardOverlay = card.querySelector('.card-overlay');
        
        card.addEventListener('mouseenter', function() {
            // Scale image
            if (cardImg) {
                cardImg.style.transform = 'scale(1.1)';
                cardImg.style.transition = 'transform 0.3s ease';
            }
            
            // Show overlay
            if (cardOverlay) {
                cardOverlay.style.opacity = '1';
                cardOverlay.style.visibility = 'visible';
            }
            
            // Add glow effect
            this.style.boxShadow = '0 15px 35px rgba(0,123,255,0.3)';
            this.style.transform = 'translateY(-10px)';
        });
        
        card.addEventListener('mouseleave', function() {
            // Reset image
            if (cardImg) {
                cardImg.style.transform = 'scale(1)';
            }
            
            // Hide overlay
            if (cardOverlay) {
                cardOverlay.style.opacity = '0';
                cardOverlay.style.visibility = 'hidden';
            }
            
            // Remove glow effect
            this.style.boxShadow = '0 5px 20px rgba(0,0,0,0.15)';
            this.style.transform = 'translateY(0)';
        });
    });
}

function initBrandLogosAnimation() {
    const brandLogos = document.querySelectorAll('.brand-logo img');
    
    brandLogos.forEach(logo => {
        logo.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.2) rotate(5deg)';
            this.style.transition = 'transform 0.3s ease';
            this.style.filter = 'brightness(1.2)';
        });
        
        logo.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1) rotate(0deg)';
            this.style.filter = 'brightness(1)';
        });
    });
}

function initFeatureBoxesAnimation() {
    const featureBoxes = document.querySelectorAll('.feature-box');
    
    featureBoxes.forEach(box => {
        const icon = box.querySelector('.feature-icon i');
        
        box.addEventListener('mouseenter', function() {
            if (icon) {
                icon.style.animation = 'pulse 1s infinite';
                icon.style.color = '#007bff';
                icon.style.transform = 'scale(1.1)';
            }
            
            this.style.transform = 'translateY(-10px)';
            this.style.transition = 'transform 0.3s ease';
        });
        
        box.addEventListener('mouseleave', function() {
            if (icon) {
                icon.style.animation = 'none';
                icon.style.transform = 'scale(1)';
            }
            
            this.style.transform = 'translateY(0)';
        });
    });
}

function initFloatingElements() {
    const floatingCar = document.querySelector('.floating-car');
    const floatingStar = document.querySelector('.floating-star');
    
    if (floatingCar) {
        setInterval(() => {
            floatingCar.style.transform = `translateY(${Math.sin(Date.now() * 0.001) * 20}px)`;
        }, 16);
    }
    
    if (floatingStar) {
        setInterval(() => {
            floatingStar.style.transform = `translateY(${Math.cos(Date.now() * 0.0015) * 15}px) rotate(${Date.now() * 0.001}rad)`;
        }, 16);
    }
}

function initStatsCounter() {
    const statsSection = document.querySelector('#stats');
    if (!statsSection) return;
    
    const stats = [
        { element: '#cars-count', target: 150, suffix: '+' },
        { element: '#customers-count', target: 500, suffix: '+' },
        { element: '#years-count', target: 10, suffix: '+' },
        { element: '#satisfaction-count', target: 98, suffix: '%' }
    ];
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                stats.forEach(stat => {
                    const element = document.querySelector(stat.element);
                    if (element) {
                        animateCounter(element, stat.target, stat.suffix);
                    }
                });
                observer.unobserve(entry.target);
            }
        });
    });
    
    observer.observe(statsSection);
}

function animateCounter(element, target, suffix = '') {
    let current = 0;
    const increment = target / 100;
    const timer = setInterval(() => {
        current += increment;
        if (current >= target) {
            current = target;
            clearInterval(timer);
        }
        element.textContent = Math.floor(current) + suffix;
    }, 20);
}

// Smooth scroll for navigation links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            const offsetTop = target.offsetTop - 80; // Account for fixed navbar
            window.scrollTo({
                top: offsetTop,
                behavior: 'smooth'
            });
        }
    });
});

// Add parallax effect to hero section
window.addEventListener('scroll', function() {
    const scrolled = window.pageYOffset;
    const heroSection = document.querySelector('.hero-section');
    
    if (heroSection) {
        const speed = 0.5;
        heroSection.style.transform = `translateY(${scrolled * speed}px)`;
    }
});

// Add scroll reveal animation for sections
const revealSections = document.querySelectorAll('.section');
const sectionObserver = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.classList.add('revealed');
            sectionObserver.unobserve(entry.target);
        }
    });
}, {
    threshold: 0.1
});

revealSections.forEach(section => {
    sectionObserver.observe(section);
});

// Add custom CSS for revealed sections
const style = document.createElement('style');
style.textContent = `
    .section {
        opacity: 0;
        transform: translateY(50px);
        transition: opacity 0.6s ease, transform 0.6s ease;
    }
    
    .section.revealed {
        opacity: 1;
        transform: translateY(0);
    }
    
    .floating-elements {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        overflow: hidden;
    }
    
    .floating-car {
        position: absolute;
        top: 20%;
        right: 10%;
        font-size: 3rem;
        color: rgba(255,255,255,0.1);
        animation: float 6s ease-in-out infinite;
    }
    
    .floating-star {
        position: absolute;
        bottom: 30%;
        left: 15%;
        font-size: 2rem;
        color: rgba(255,255,255,0.1);
        animation: float 4s ease-in-out infinite reverse;
    }
    
    @keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-20px); }
    }
    
    .card-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0,0,0,0.7);
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
    }
    
    .card-img-container {
        overflow: hidden;
        border-radius: 10px 10px 0 0;
    }
    
    .card-badge {
        position: absolute;
        top: 10px;
        right: 10px;
        z-index: 2;
    }
    
    .brand-logo {
        padding: 20px;
        background: white;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }
    
    .brand-logo:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }
    
    .feature-box {
        padding: 30px 20px;
        border-radius: 10px;
        transition: all 0.3s ease;
    }
    
    .feature-box:hover {
        background: white;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }
    
    .feature-icon {
        transition: all 0.3s ease;
    }
`;
document.head.appendChild(style);
