<?php
// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_NAME', 'car_showroom');
define('DB_USER', 'root');
define('DB_PASS', '');

// إعدادات الموقع
define('SITE_NAME', 'معرض السيارات الفاخرة');
define('SITE_NAME_EN', 'Luxury Car Showroom');
define('SITE_URL', 'http://localhost/dyaralkaram.com/New folder/');

// إعدادات اللغة
define('DEFAULT_LANG', 'ar');
define('SUPPORTED_LANGS', ['ar', 'en']);

// إعدادات الأمان
define('CSRF_TOKEN_NAME', 'csrf_token');

// بدء الجلسة
session_start();

// تحديد اللغة الحالية
if (!isset($_SESSION['lang'])) {
    $_SESSION['lang'] = DEFAULT_LANG;
}

if (isset($_GET['lang']) && in_array($_GET['lang'], SUPPORTED_LANGS)) {
    $_SESSION['lang'] = $_GET['lang'];
}

$current_lang = $_SESSION['lang'];

// تحميل ملف الترجمة
$lang_file = __DIR__ . "/lang/{$current_lang}/translations.php";
if (file_exists($lang_file)) {
    $translations = include $lang_file;
} else {
    $translations = [];
}

// دالة الترجمة
function t($key, $default = '') {
    global $translations;
    return isset($translations[$key]) ? $translations[$key] : ($default ?: $key);
}

// دالة الاتصال بقاعدة البيانات
function getDBConnection() {
    try {
        $pdo = new PDO(
            "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
            DB_USER,
            DB_PASS,
            [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
            ]
        );
        return $pdo;
    } catch (PDOException $e) {
        die("خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage());
    }
}

// دالة توليد CSRF Token
function generateCSRFToken() {
    if (!isset($_SESSION[CSRF_TOKEN_NAME])) {
        $_SESSION[CSRF_TOKEN_NAME] = bin2hex(random_bytes(32));
    }
    return $_SESSION[CSRF_TOKEN_NAME];
}

// دالة التحقق من CSRF Token
function verifyCSRFToken($token) {
    return isset($_SESSION[CSRF_TOKEN_NAME]) && hash_equals($_SESSION[CSRF_TOKEN_NAME], $token);
}

// دالة تنظيف البيانات
function sanitize($data) {
    return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
}

// دالة إعادة التوجيه
function redirect($url) {
    header("Location: $url");
    exit();
}

// دالة عرض الرسائل
function showMessage($message, $type = 'info') {
    $_SESSION['message'] = ['text' => $message, 'type' => $type];
}

function getMessage() {
    if (isset($_SESSION['message'])) {
        $message = $_SESSION['message'];
        unset($_SESSION['message']);
        return $message;
    }
    return null;
}

// إعدادات التصفح
$is_rtl = ($current_lang === 'ar');
$text_direction = $is_rtl ? 'rtl' : 'ltr';
$bootstrap_rtl = $is_rtl ? '.rtl' : '';
?>
