<?php
require_once 'config.php';

// Get car ID from URL
$car_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

// Sample car data (in real application, this would come from database)
$cars = [
    1 => [
        'id' => 1,
        'name' => 'BMW X5 2024',
        'brand' => 'BMW',
        'model' => 'X5',
        'year' => 2024,
        'price' => 450000,
        'mileage' => 0,
        'fuel_type' => 'petrol',
        'transmission' => 'automatic',
        'engine' => '3.0L Twin Turbo',
        'color' => 'أسود معدني',
        'condition' => 'new',
        'location' => 'الرياض',
        'description' => 'BMW X5 2024 الجديدة كلياً بمحرك قوي وتصميم أنيق. تتميز بالفخامة والأداء العالي مع أحدث التقنيات.',
        'features' => [
            'نظام الملاحة GPS',
            'كاميرا خلفية',
            'مقاعد جلدية',
            'نظام صوتي متطور',
            'تكييف أوتوماتيك',
            'نوافذ كهربائية',
            'ABS',
            'وسائد هوائية',
            'مثبت سرعة',
            'إضاءة LED'
        ],
        'images' => [
            'https://via.placeholder.com/800x600/007bff/ffffff?text=BMW+X5+Main',
            'https://via.placeholder.com/800x600/0056b3/ffffff?text=BMW+X5+Interior',
            'https://via.placeholder.com/800x600/004085/ffffff?text=BMW+X5+Side',
            'https://via.placeholder.com/800x600/002a5c/ffffff?text=BMW+X5+Back'
        ],
        'featured' => true
    ],
    2 => [
        'id' => 2,
        'name' => 'Mercedes C-Class 2023',
        'brand' => 'Mercedes',
        'model' => 'C-Class',
        'year' => 2023,
        'price' => 320000,
        'mileage' => 15000,
        'fuel_type' => 'petrol',
        'transmission' => 'automatic',
        'engine' => '2.0L Turbo',
        'color' => 'أبيض لؤلؤي',
        'condition' => 'excellent',
        'location' => 'جدة',
        'description' => 'مرسيدس C-Class 2023 بحالة ممتازة، قليلة الاستخدام مع صيانة دورية منتظمة.',
        'features' => [
            'نظام الملاحة',
            'كاميرا 360 درجة',
            'مقاعد جلدية مدفأة',
            'نظام صوتي Burmester',
            'تكييف مناخي',
            'فتحة سقف',
            'نظام الأمان النشط',
            'شاشة لمس',
            'مثبت سرعة ذكي',
            'إضاءة محيطية'
        ],
        'images' => [
            'https://via.placeholder.com/800x600/28a745/ffffff?text=Mercedes+C-Class+Main',
            'https://via.placeholder.com/800x600/1e7e34/ffffff?text=Mercedes+Interior',
            'https://via.placeholder.com/800x600/155724/ffffff?text=Mercedes+Side',
            'https://via.placeholder.com/800x600/0d4017/ffffff?text=Mercedes+Back'
        ],
        'featured' => true
    ]
];

// Check if car exists
if (!isset($cars[$car_id])) {
    showMessage(t('car_not_found'), 'error');
    redirect('cars.php');
}

$car = $cars[$car_id];
$page_title = $car['name'] . ' - ' . t('car_details');
$page_scripts = ['assets/js/car-details.js'];

include 'includes/header.php';
?>

<!-- Car Details Header -->
<section class="page-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <nav aria-label="breadcrumb" class="mb-3">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="index.php" class="text-white"><?php echo t('home'); ?></a></li>
                        <li class="breadcrumb-item"><a href="cars.php" class="text-white"><?php echo t('cars'); ?></a></li>
                        <li class="breadcrumb-item active text-white-50"><?php echo htmlspecialchars($car['name']); ?></li>
                    </ol>
                </nav>
                <h1 class="display-5 fw-bold mb-3" data-aos="fade-right">
                    <?php echo htmlspecialchars($car['name']); ?>
                </h1>
                <div class="car-meta" data-aos="fade-right" data-aos-delay="200">
                    <span class="badge bg-light text-dark me-2">
                        <i class="fas fa-calendar me-1"></i><?php echo $car['year']; ?>
                    </span>
                    <span class="badge bg-light text-dark me-2">
                        <i class="fas fa-tachometer-alt me-1"></i><?php echo number_format($car['mileage']); ?> <?php echo t('km'); ?>
                    </span>
                    <span class="badge bg-light text-dark">
                        <i class="fas fa-map-marker-alt me-1"></i><?php echo $car['location']; ?>
                    </span>
                </div>
            </div>
            <div class="col-lg-4 text-lg-end" data-aos="fade-left" data-aos-delay="300">
                <div class="price-box">
                    <h2 class="price-amount fw-bold mb-2"><?php echo number_format($car['price']); ?> <?php echo t('currency'); ?></h2>
                    <div class="price-actions">
                        <button class="btn btn-light me-2 favorite-btn" data-car-id="<?php echo $car['id']; ?>">
                            <i class="far fa-heart me-1"></i><?php echo t('add_to_favorites', 'إضافة للمفضلة'); ?>
                        </button>
                        <button class="btn btn-outline-light share-btn" data-car-id="<?php echo $car['id']; ?>">
                            <i class="fas fa-share-alt me-1"></i><?php echo t('share', 'مشاركة'); ?>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Car Details Content -->
<section class="car-details-section py-5">
    <div class="container">
        <div class="row">
            <!-- Car Gallery -->
            <div class="col-lg-8 mb-4">
                <div class="car-gallery" data-aos="fade-up">
                    <div class="main-image-container">
                        <img src="<?php echo $car['images'][0]; ?>" alt="<?php echo htmlspecialchars($car['name']); ?>" 
                             class="main-image img-fluid" id="mainImage">
                        
                        <!-- Image Navigation -->
                        <div class="image-nav">
                            <button class="btn btn-primary btn-sm prev-btn" id="prevImage">
                                <i class="fas fa-chevron-left"></i>
                            </button>
                            <button class="btn btn-primary btn-sm next-btn" id="nextImage">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                        
                        <!-- Image Counter -->
                        <div class="image-counter">
                            <span id="currentImageIndex">1</span> / <?php echo count($car['images']); ?>
                        </div>
                    </div>
                    
                    <!-- Thumbnail Images -->
                    <div class="thumbnail-images">
                        <?php foreach ($car['images'] as $index => $image): ?>
                            <img src="<?php echo $image; ?>" alt="<?php echo htmlspecialchars($car['name']); ?>" 
                                 class="thumbnail <?php echo $index === 0 ? 'active' : ''; ?>" 
                                 data-index="<?php echo $index; ?>">
                        <?php endforeach; ?>
                    </div>
                </div>
                
                <!-- Car Description -->
                <div class="car-description mt-4" data-aos="fade-up" data-aos-delay="200">
                    <h3 class="mb-3"><?php echo t('description', 'الوصف'); ?></h3>
                    <p class="lead"><?php echo htmlspecialchars($car['description']); ?></p>
                </div>
                
                <!-- Car Features -->
                <div class="car-features mt-4" data-aos="fade-up" data-aos-delay="300">
                    <h3 class="mb-3"><?php echo t('features'); ?></h3>
                    <div class="row">
                        <?php foreach ($car['features'] as $feature): ?>
                            <div class="col-md-6 mb-2">
                                <div class="feature-item">
                                    <i class="fas fa-check-circle text-success me-2"></i>
                                    <?php echo htmlspecialchars($feature); ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
            
            <!-- Car Specifications -->
            <div class="col-lg-4">
                <div class="specs-card" data-aos="fade-up" data-aos-delay="400">
                    <h3 class="mb-4"><?php echo t('specifications'); ?></h3>
                    
                    <div class="specs-table">
                        <div class="spec-item">
                            <span class="spec-label"><?php echo t('brand', 'الماركة'); ?></span>
                            <span class="spec-value"><?php echo $car['brand']; ?></span>
                        </div>
                        <div class="spec-item">
                            <span class="spec-label"><?php echo t('model', 'الموديل'); ?></span>
                            <span class="spec-value"><?php echo $car['model']; ?></span>
                        </div>
                        <div class="spec-item">
                            <span class="spec-label"><?php echo t('year'); ?></span>
                            <span class="spec-value"><?php echo $car['year']; ?></span>
                        </div>
                        <div class="spec-item">
                            <span class="spec-label"><?php echo t('mileage'); ?></span>
                            <span class="spec-value"><?php echo number_format($car['mileage']); ?> <?php echo t('km'); ?></span>
                        </div>
                        <div class="spec-item">
                            <span class="spec-label"><?php echo t('fuel_type'); ?></span>
                            <span class="spec-value"><?php echo t($car['fuel_type']); ?></span>
                        </div>
                        <div class="spec-item">
                            <span class="spec-label"><?php echo t('transmission'); ?></span>
                            <span class="spec-value"><?php echo t($car['transmission']); ?></span>
                        </div>
                        <div class="spec-item">
                            <span class="spec-label"><?php echo t('engine'); ?></span>
                            <span class="spec-value"><?php echo $car['engine']; ?></span>
                        </div>
                        <div class="spec-item">
                            <span class="spec-label"><?php echo t('color'); ?></span>
                            <span class="spec-value"><?php echo $car['color']; ?></span>
                        </div>
                        <div class="spec-item">
                            <span class="spec-label"><?php echo t('condition'); ?></span>
                            <span class="spec-value">
                                <span class="badge bg-<?php echo $car['condition'] === 'new' ? 'success' : 'info'; ?>">
                                    <?php echo t($car['condition']); ?>
                                </span>
                            </span>
                        </div>
                        <div class="spec-item">
                            <span class="spec-label"><?php echo t('location'); ?></span>
                            <span class="spec-value"><?php echo $car['location']; ?></span>
                        </div>
                    </div>
                </div>
                
                <!-- Contact Card -->
                <div class="contact-card mt-4" data-aos="fade-up" data-aos-delay="500">
                    <h4 class="mb-3"><?php echo t('interested_in_car', 'مهتم بهذه السيارة؟'); ?></h4>
                    <p class="text-muted mb-3"><?php echo t('contact_us_for_details', 'اتصل بنا للحصول على مزيد من التفاصيل'); ?></p>
                    
                    <div class="contact-buttons">
                        <a href="contact.php?car=<?php echo $car['id']; ?>" class="btn btn-primary w-100 mb-2">
                            <i class="fas fa-envelope me-2"></i><?php echo t('send_inquiry', 'إرسال استفسار'); ?>
                        </a>
                        <a href="tel:+966123456789" class="btn btn-success w-100 mb-2">
                            <i class="fas fa-phone me-2"></i><?php echo t('call_now', 'اتصل الآن'); ?>
                        </a>
                        <a href="https://wa.me/966123456789?text=<?php echo urlencode('مرحباً، أنا مهتم بسيارة ' . $car['name']); ?>" 
                           target="_blank" class="btn btn-success w-100">
                            <i class="fab fa-whatsapp me-2"></i><?php echo t('whatsapp', 'واتساب'); ?>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Related Cars -->
<section class="related-cars-section py-5 bg-light">
    <div class="container">
        <div class="section-title" data-aos="fade-up">
            <h2><?php echo t('related_cars', 'سيارات مشابهة'); ?></h2>
            <p><?php echo t('you_might_also_like', 'قد تعجبك أيضاً'); ?></p>
        </div>
        
        <div class="row">
            <!-- This would typically show related cars from database -->
            <div class="col-lg-4 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="100">
                <div class="card card-custom">
                    <img src="https://via.placeholder.com/400x250/6c757d/ffffff?text=Related+Car+1" class="card-img-top" alt="Related Car">
                    <div class="card-body">
                        <h5 class="card-title">BMW X3 2023</h5>
                        <p class="card-text text-muted">
                            <i class="fas fa-calendar me-1"></i>2023 • 
                            <i class="fas fa-tachometer-alt me-1"></i>12,000 <?php echo t('km'); ?>
                        </p>
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="price text-primary fw-bold">380,000 <?php echo t('currency'); ?></span>
                            <a href="car-details.php?id=7" class="btn btn-outline-primary btn-sm">
                                <?php echo t('view_details'); ?>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="200">
                <div class="card card-custom">
                    <img src="https://via.placeholder.com/400x250/6c757d/ffffff?text=Related+Car+2" class="card-img-top" alt="Related Car">
                    <div class="card-body">
                        <h5 class="card-title">Audi Q5 2023</h5>
                        <p class="card-text text-muted">
                            <i class="fas fa-calendar me-1"></i>2023 • 
                            <i class="fas fa-tachometer-alt me-1"></i>8,000 <?php echo t('km'); ?>
                        </p>
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="price text-primary fw-bold">420,000 <?php echo t('currency'); ?></span>
                            <a href="car-details.php?id=8" class="btn btn-outline-primary btn-sm">
                                <?php echo t('view_details'); ?>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="300">
                <div class="card card-custom">
                    <img src="https://via.placeholder.com/400x250/6c757d/ffffff?text=Related+Car+3" class="card-img-top" alt="Related Car">
                    <div class="card-body">
                        <h5 class="card-title">Mercedes GLC 2022</h5>
                        <p class="card-text text-muted">
                            <i class="fas fa-calendar me-1"></i>2022 • 
                            <i class="fas fa-tachometer-alt me-1"></i>25,000 <?php echo t('km'); ?>
                        </p>
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="price text-primary fw-bold">350,000 <?php echo t('currency'); ?></span>
                            <a href="car-details.php?id=9" class="btn btn-outline-primary btn-sm">
                                <?php echo t('view_details'); ?>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<?php include 'includes/footer.php'; ?>
