// ===== Cars Page Specific JavaScript =====

document.addEventListener('DOMContentLoaded', function() {
    
    // Initialize cars page functionality
    initFiltersForm();
    initCarCards();
    initFavoriteButtons();
    initShareButtons();
    initGridAnimation();
    
});

function initFiltersForm() {
    const filtersForm = document.getElementById('filtersForm');
    const formInputs = filtersForm.querySelectorAll('input, select');
    
    // Auto-submit form on filter change
    formInputs.forEach(input => {
        input.addEventListener('change', function() {
            // Add loading state
            showLoadingState();
            
            // Submit form after short delay for better UX
            setTimeout(() => {
                filtersForm.submit();
            }, 300);
        });
    });
    
    // Search input with debounce
    const searchInput = filtersForm.querySelector('input[name="search"]');
    if (searchInput) {
        let searchTimeout;
        
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            
            searchTimeout = setTimeout(() => {
                showLoadingState();
                filtersForm.submit();
            }, 800);
        });
    }
}

function initCarCards() {
    const carCards = document.querySelectorAll('.car-item .card-custom');
    
    carCards.forEach(card => {
        const cardImg = card.querySelector('.card-img-top');
        const cardOverlay = card.querySelector('.card-overlay');
        
        // Enhanced hover effects
        card.addEventListener('mouseenter', function() {
            // Image zoom effect
            if (cardImg) {
                cardImg.style.transform = 'scale(1.1)';
                cardImg.style.transition = 'transform 0.4s ease';
            }
            
            // Show overlay with animation
            if (cardOverlay) {
                cardOverlay.style.opacity = '1';
                cardOverlay.style.visibility = 'visible';
                cardOverlay.style.transform = 'scale(1)';
            }
            
            // Card elevation
            this.style.transform = 'translateY(-15px) scale(1.02)';
            this.style.boxShadow = '0 20px 40px rgba(0,123,255,0.2)';
            this.style.transition = 'all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275)';
        });
        
        card.addEventListener('mouseleave', function() {
            // Reset image
            if (cardImg) {
                cardImg.style.transform = 'scale(1)';
            }
            
            // Hide overlay
            if (cardOverlay) {
                cardOverlay.style.opacity = '0';
                cardOverlay.style.visibility = 'hidden';
                cardOverlay.style.transform = 'scale(0.9)';
            }
            
            // Reset card
            this.style.transform = 'translateY(0) scale(1)';
            this.style.boxShadow = '0 5px 20px rgba(0,0,0,0.15)';
        });
        
        // Add click animation
        card.addEventListener('click', function(e) {
            if (!e.target.closest('.btn')) {
                this.style.transform = 'scale(0.98)';
                setTimeout(() => {
                    this.style.transform = 'translateY(-15px) scale(1.02)';
                }, 150);
            }
        });
    });
}

function initFavoriteButtons() {
    const favoriteButtons = document.querySelectorAll('.favorite-btn');
    
    favoriteButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            const carId = this.dataset.carId;
            const icon = this.querySelector('i');
            
            // Toggle favorite state
            if (this.classList.contains('active')) {
                // Remove from favorites
                this.classList.remove('active');
                icon.className = 'far fa-heart';
                removeFavorite(carId);
                showNotification('تم إزالة السيارة من المفضلة', 'info');
            } else {
                // Add to favorites
                this.classList.add('active');
                icon.className = 'fas fa-heart';
                addFavorite(carId);
                showNotification('تم إضافة السيارة للمفضلة', 'success');
            }
            
            // Add animation
            this.style.transform = 'scale(1.2)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 200);
        });
    });
    
    // Load favorites from localStorage
    loadFavorites();
}

function initShareButtons() {
    const shareButtons = document.querySelectorAll('.share-btn');
    
    shareButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            const carId = this.dataset.carId;
            const carUrl = `${window.location.origin}/car-details.php?id=${carId}`;
            
            // Check if Web Share API is supported
            if (navigator.share) {
                navigator.share({
                    title: 'شاهد هذه السيارة المميزة',
                    text: 'اكتشف هذه السيارة الرائعة في معرضنا',
                    url: carUrl
                }).then(() => {
                    showNotification('تم مشاركة السيارة بنجاح', 'success');
                }).catch(() => {
                    fallbackShare(carUrl);
                });
            } else {
                fallbackShare(carUrl);
            }
            
            // Add animation
            this.style.transform = 'scale(1.2)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 200);
        });
    });
}

function initGridAnimation() {
    // Stagger animation for car cards
    const carItems = document.querySelectorAll('.car-item');
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry, index) => {
            if (entry.isIntersecting) {
                setTimeout(() => {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }, index * 100);
                observer.unobserve(entry.target);
            }
        });
    }, {
        threshold: 0.1
    });
    
    carItems.forEach(item => {
        item.style.opacity = '0';
        item.style.transform = 'translateY(30px)';
        item.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(item);
    });
}

function showLoadingState() {
    const carsGrid = document.getElementById('carsGrid');
    if (carsGrid) {
        carsGrid.style.opacity = '0.5';
        carsGrid.style.pointerEvents = 'none';
        
        // Add loading spinner
        const loadingSpinner = document.createElement('div');
        loadingSpinner.className = 'loading-overlay';
        loadingSpinner.innerHTML = `
            <div class="text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2">جاري التحميل...</p>
            </div>
        `;
        carsGrid.appendChild(loadingSpinner);
    }
}

function addFavorite(carId) {
    let favorites = JSON.parse(localStorage.getItem('favorites') || '[]');
    if (!favorites.includes(carId)) {
        favorites.push(carId);
        localStorage.setItem('favorites', JSON.stringify(favorites));
    }
}

function removeFavorite(carId) {
    let favorites = JSON.parse(localStorage.getItem('favorites') || '[]');
    favorites = favorites.filter(id => id !== carId);
    localStorage.setItem('favorites', JSON.stringify(favorites));
}

function loadFavorites() {
    const favorites = JSON.parse(localStorage.getItem('favorites') || '[]');
    
    favorites.forEach(carId => {
        const favoriteBtn = document.querySelector(`[data-car-id="${carId}"]`);
        if (favoriteBtn && favoriteBtn.classList.contains('favorite-btn')) {
            favoriteBtn.classList.add('active');
            const icon = favoriteBtn.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-heart';
            }
        }
    });
}

function fallbackShare(url) {
    // Copy to clipboard
    if (navigator.clipboard) {
        navigator.clipboard.writeText(url).then(() => {
            showNotification('تم نسخ رابط السيارة', 'success');
        }).catch(() => {
            showShareModal(url);
        });
    } else {
        showShareModal(url);
    }
}

function showShareModal(url) {
    // Create share modal
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">مشاركة السيارة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>شارك هذه السيارة:</p>
                    <div class="input-group">
                        <input type="text" class="form-control" value="${url}" readonly>
                        <button class="btn btn-primary" onclick="copyToClipboard('${url}')">نسخ</button>
                    </div>
                    <div class="social-share mt-3">
                        <a href="https://wa.me/?text=${encodeURIComponent(url)}" target="_blank" class="btn btn-success me-2">
                            <i class="fab fa-whatsapp"></i> واتساب
                        </a>
                        <a href="https://twitter.com/intent/tweet?url=${encodeURIComponent(url)}" target="_blank" class="btn btn-info me-2">
                            <i class="fab fa-twitter"></i> تويتر
                        </a>
                        <a href="https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}" target="_blank" class="btn btn-primary">
                            <i class="fab fa-facebook"></i> فيسبوك
                        </a>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();
    
    modal.addEventListener('hidden.bs.modal', () => {
        modal.remove();
    });
}

function copyToClipboard(text) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    document.body.appendChild(textArea);
    textArea.select();
    document.execCommand('copy');
    document.body.removeChild(textArea);
    showNotification('تم نسخ الرابط', 'success');
}

// Add custom styles for loading overlay
const style = document.createElement('style');
style.textContent = `
    .loading-overlay {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 10;
        background: rgba(255,255,255,0.9);
        padding: 20px;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    
    .card-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0,0,0,0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
        transform: scale(0.9);
    }
    
    .social-share .btn {
        border-radius: 25px;
        padding: 8px 16px;
    }
`;
document.head.appendChild(style);
